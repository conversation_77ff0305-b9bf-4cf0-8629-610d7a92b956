'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, AlertTriangle } from 'lucide-react';
import { useNotification } from '@/contexts/NotificationContext';

interface FormData {
  name: string;
  phone: string;
  location: string;
  notes: string;
  serviceType: 'recurring' | 'scheduled' | 'none';
  serviceInterval: number;
  scheduledDate: string;
}

interface FormErrors {
  name?: string;
  phone?: string;
  location?: string;
  serviceInterval?: string;
  scheduledDate?: string;
}

const NewClientPage = () => {
  const router = useRouter();
  const { showSuccess, showError } = useNotification();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    phone: '+91',
    location: '',
    notes: '',
    serviceType: 'none',
    serviceInterval: 3,
    scheduledDate: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const serviceIntervalOptions = [
    { value: 1, label: '1 month' },
    { value: 2, label: '2 months' },
    { value: 3, label: '3 months' },
    { value: 4, label: '4 months' },
    { value: 5, label: '5 months' },
    { value: 6, label: '6 months' },
    { value: 12, label: '1 year' }
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Client name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters long';
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+91[6-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid Indian phone number (e.g., +************)';
    }

    // Location validation
    if (!formData.location.trim()) {
      newErrors.location = 'Service location is required';
    } else if (formData.location.trim().length < 5) {
      newErrors.location = 'Location must be at least 5 characters long';
    }

    // Service type specific validation
    if (formData.serviceType === 'recurring') {
      if (!serviceIntervalOptions.find(option => option.value === formData.serviceInterval)) {
        newErrors.serviceInterval = 'Please select a valid service interval';
      }
    } else if (formData.serviceType === 'scheduled') {
      if (!formData.scheduledDate) {
        newErrors.scheduledDate = 'Scheduled date is required';
      } else {
        const selectedDate = new Date(formData.scheduledDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (selectedDate < today) {
          newErrors.scheduledDate = 'Scheduled date cannot be in the past';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'phone') {
      // Ensure phone always starts with +91
      let phoneValue = value;
      if (!phoneValue.startsWith('+91')) {
        phoneValue = '+91' + phoneValue.replace(/^\+?91?/, '');
      }
      // Limit to 13 characters (+91 + 10 digits)
      if (phoneValue.length <= 13) {
        setFormData(prev => ({ ...prev, [name]: phoneValue }));
      }
    } else if (name === 'serviceInterval') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else if (name === 'serviceType') {
      // Reset related fields when service type changes
      setFormData(prev => ({
        ...prev,
        [name]: value as 'recurring' | 'scheduled' | 'none',
        serviceInterval: value === 'recurring' ? 3 : prev.serviceInterval,
        scheduledDate: value === 'scheduled' ? prev.scheduledDate : ''
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error for this field when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setSubmitError(null);

    try {
      const response = await fetch('/api/clients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        showSuccess('Client Created', 'New client has been created successfully.');
        router.push('/clients');
      } else {
        const errorMsg = result.error || 'Failed to create client';
        setSubmitError(errorMsg);
        showError('Creation Failed', errorMsg);
      }
    } catch (error) {
      const errorMsg = 'Failed to create client. Please try again.';
      setSubmitError(errorMsg);
      showError('Creation Failed', errorMsg);
      console.error('Error creating client:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 px-4 sm:px-6 lg:px-8 py-6">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/clients"
            className="inline-flex items-center text-indigo-400 hover:text-indigo-300 mb-4 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Clients
          </Link>
          <h1 className="text-xl font-bold text-slate-200 mb-2">Create a new client profile for RO service management</h1>
        </div>

        {/* Form */}
        <div className="futuristic-card">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Submit Error */}
            {submitError && (
              <div className="bg-red-900/20 border border-red-500/30 rounded-md p-4">
                <div className="flex">
                  <AlertTriangle className="w-5 h-5 text-red-400 mr-3 flex-shrink-0" />
                  <div className="text-sm text-red-400">{submitError}</div>
                </div>
              </div>
            )}

            {/* Client Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-slate-200 mb-2">
                Client Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`input-field ${errors.name ? 'border-red-500' : ''}`}
                placeholder="Enter client's full name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-400">{errors.name}</p>}
            </div>

            {/* Phone Number */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-slate-200 mb-2">
                Phone Number *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`input-field ${errors.phone ? 'border-red-500' : ''}`}
                placeholder="+************"
              />
              {errors.phone && <p className="mt-1 text-sm text-red-400">{errors.phone}</p>}
              <p className="mt-1 text-sm text-slate-400">Format: +91 followed by 10 digits</p>
            </div>

            {/* Service Location */}
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-slate-200 mb-2">
                Service Location *
              </label>
              <textarea
                id="location"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                rows={3}
                className={`input-field resize-none ${errors.location ? 'border-red-500' : ''}`}
                placeholder="Enter complete address where RO service will be provided"
              />
              {errors.location && <p className="mt-1 text-sm text-red-400">{errors.location}</p>}
            </div>

            {/* Service Type */}
            <div>
              <label htmlFor="serviceType" className="block text-sm font-medium text-slate-200 mb-2">
                Service Type *
              </label>
              <select
                id="serviceType"
                name="serviceType"
                value={formData.serviceType}
                onChange={handleInputChange}
                className="input-field"
              >
                <option value="none">No Service Schedule</option>
                <option value="recurring">Recurring Service</option>
                <option value="scheduled">Scheduled Service</option>
              </select>
              <p className="mt-1 text-sm text-slate-400">
                {formData.serviceType === 'none' 
                  ? 'Client will be added without any service schedule'
                  : formData.serviceType === 'recurring'
                  ? 'Service will repeat at regular intervals'
                  : 'Service will be scheduled for a specific date'}
              </p>
            </div>

            {/* Service Interval (for recurring services) */}
            {formData.serviceType === 'recurring' && (
              <div>
                <label htmlFor="serviceInterval" className="block text-sm font-medium text-slate-200 mb-2">
                  Service Interval *
                </label>
                <select
                  id="serviceInterval"
                  name="serviceInterval"
                  value={formData.serviceInterval}
                  onChange={handleInputChange}
                  className={`input-field ${errors.serviceInterval ? 'border-red-500' : ''}`}
                >
                  {serviceIntervalOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.serviceInterval && <p className="mt-1 text-sm text-red-400">{errors.serviceInterval}</p>}
                <p className="mt-1 text-sm text-slate-400">How often should the RO be serviced?</p>
              </div>
            )}

            {/* Scheduled Date (for scheduled services) */}
            {formData.serviceType === 'scheduled' && (
              <div>
                <label htmlFor="scheduledDate" className="block text-sm font-medium text-slate-200 mb-2">
                  Scheduled Service Date *
                </label>
                <input
                  type="date"
                  id="scheduledDate"
                  name="scheduledDate"
                  value={formData.scheduledDate}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className={`input-field ${errors.scheduledDate ? 'border-red-500' : ''}`}
                />
                {errors.scheduledDate && <p className="mt-1 text-sm text-red-400">{errors.scheduledDate}</p>}
                <p className="mt-1 text-sm text-slate-400">Select the specific date for the next service</p>
              </div>
            )}

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-slate-200 mb-2">
                Notes (Optional)
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={4}
                className="input-field resize-none"
                placeholder="Any additional information about the client or service requirements..."
              />
            </div>

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row sm:justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-slate-600">
              <Link
                href="/clients"
                className="btn-secondary w-full sm:w-auto text-center min-h-[44px] flex items-center justify-center"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="btn-primary w-full sm:w-auto inline-flex items-center justify-center min-h-[44px] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Create Client
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default NewClientPage;