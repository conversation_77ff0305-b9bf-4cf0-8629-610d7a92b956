'use client';

import { useState, useEffect } from 'react';
import { Calendar, Clock, AlertTriangle, Users, CheckCircle } from 'lucide-react';
import ClientCard from './ClientCard';

interface Client {
  _id: string;
  name: string;
  phone: string;
  location: string;
  notes?: string;
  serviceType: 'recurring' | 'scheduled';
  serviceInterval?: number;
  scheduledDate?: string;
  nextServiceDate: string;
}

interface CompletedService {
  _id: string;
  clientId: {
    _id: string;
    name: string;
    phone: string;
    location: string;
  } | null;
  serviceDate: string;
  status: string;
  notes?: string;
  completedAt: string;
  createdAt: string;
  updatedAt: string;
}

interface DashboardData {
  today: Client[];
  tomorrow: Client[];
  overdue: Client[];
  upcoming: Client[];
  completed: CompletedService[];
  stats: {
    totalClients: number;
    todayCount: number;
    tomorrowCount: number;
    overdueCount: number;
    upcomingCount: number;
    completedCount: number;
    todayCompletedCount: number;
  };
}

const Dashboard = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);

    // Refresh when window gains focus (user returns to tab)
    const handleFocus = () => fetchDashboardData();
    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      console.log('Dashboard - fetching data...');
      const response = await fetch('/api/dashboard');
      const result = await response.json();

      console.log('Dashboard - API response:', result);

      if (result.success) {
        setData(result.data);
        console.log('Dashboard - data set successfully:', {
          todayCount: result.data.today.length,
          tomorrowCount: result.data.tomorrow.length,
          overdueCount: result.data.overdue.length,
          upcomingCount: result.data.upcoming.length
        });
      } else {
        setError(result.error || 'Failed to fetch dashboard data');
        console.error('Dashboard - API error:', result.error);
      }
    } catch (err) {
      setError('Failed to fetch dashboard data');
      console.error('Dashboard fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleServiceComplete = async (clientId: string) => {
    console.log('Dashboard - Service completed for client:', clientId);

    // Refresh dashboard data after service completion
    try {
      await fetchDashboardData();
      console.log('Dashboard - Data refreshed successfully after service completion');
    } catch (error) {
      console.error('Dashboard - Error refreshing data after service completion:', error);
      // Still try to refresh after a short delay
      setTimeout(() => {
        fetchDashboardData();
      }, 2000);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-slate-100 mb-2">Error Loading Dashboard</h2>
          <p className="text-slate-400 mb-4">{error}</p>
          <button
            onClick={fetchDashboardData}
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 bg-slate-900 min-h-screen">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold gradient-text mb-2">Dashboard</h1>
        <p className="text-slate-400">Manage your RO service schedule</p>
      </div>

      {/* Stats Cards - 2 per row layout (including mobile) */}
      <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-8">
        {/* Row 1 */}
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="w-8 h-8 sm:w-10 sm:h-10 text-indigo-400" />
              <div className="ml-2 sm:ml-4">
                <p className="text-xs sm:text-sm font-medium text-slate-400">Total Clients</p>
                <p className="text-xl sm:text-3xl font-bold text-slate-100">{data.stats.totalClients}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="w-2 h-2 sm:w-3 sm:h-3 bg-indigo-400 rounded-full pulse-dot"></div>
            </div>
          </div>
        </div>
        
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="w-8 h-8 sm:w-10 sm:h-10 text-red-400" />
              <div className="ml-2 sm:ml-4">
                <p className="text-xs sm:text-sm font-medium text-slate-400">Overdue</p>
                <p className="text-xl sm:text-3xl font-bold text-red-400">{data.stats.overdueCount}</p>
              </div>
            </div>
            <div className="text-right">
              {data.stats.overdueCount > 0 && (
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-400 rounded-full pulse-dot"></div>
              )}
            </div>
          </div>
        </div>

        {/* Row 2 */}
        <div className={`dashboard-stat-card ${
          data.stats.todayCount > 0
            ? 'bg-gradient-to-r from-amber-900/20 to-red-900/20 border-amber-600/30'
            : ''
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center ${
                data.stats.todayCount > 0
                  ? 'bg-amber-500 text-white'
                  : 'text-amber-400'
              }`}>
                <Clock className="w-4 h-4 sm:w-6 sm:h-6" />
              </div>
              <div className="ml-2 sm:ml-4">
                <p className="text-xs sm:text-sm font-medium text-slate-400">Today</p>
                <p className={`text-xl sm:text-3xl font-bold ${
                  data.stats.todayCount > 0 ? 'text-amber-400' : 'text-slate-100'
                }`}>
                  {data.stats.todayCount}
                </p>
              </div>
            </div>
            <div className="text-right">
              {data.stats.todayCount > 0 && (
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-amber-500 rounded-full pulse-dot"></div>
              )}
            </div>
          </div>
        </div>
        
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 sm:w-10 sm:h-10 text-cyan-400" />
              <div className="ml-2 sm:ml-4">
                <p className="text-xs sm:text-sm font-medium text-slate-400">Tomorrow</p>
                <p className="text-xl sm:text-3xl font-bold text-cyan-400">{data.stats.tomorrowCount}</p>
              </div>
            </div>
            <div className="text-right">
              {data.stats.tomorrowCount > 0 && (
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-cyan-400 rounded-full pulse-dot"></div>
              )}
            </div>
          </div>
        </div>

        {/* Row 3 */}
        <div className="dashboard-stat-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 sm:w-10 sm:h-10 text-emerald-400" />
              <div className="ml-2 sm:ml-4">
                <p className="text-xs sm:text-sm font-medium text-slate-400">Upcoming</p>
                <p className="text-xl sm:text-3xl font-bold text-emerald-400">{data.stats.upcomingCount}</p>
              </div>
            </div>
            <div className="text-right">
              {data.stats.upcomingCount > 0 && (
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-emerald-400 rounded-full pulse-dot"></div>
              )}
            </div>
          </div>
        </div>

        {/* Completed Services Box */}
        <div className="dashboard-stat-card bg-gradient-to-r from-emerald-900/20 to-green-900/20 border-emerald-600/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-emerald-500 rounded-full flex items-center justify-center">
                <CheckCircle className="w-4 h-4 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="ml-2 sm:ml-4">
                <p className="text-xs sm:text-sm font-medium text-slate-400">Completed</p>
                <p className="text-xl sm:text-3xl font-bold text-emerald-400">{data.stats.todayCompletedCount || 0}</p>
              </div>
            </div>
            <div className="text-right">
              {(data.stats.todayCompletedCount || 0) > 0 && (
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-emerald-500 rounded-full pulse-dot"></div>
              )}
            </div>
          </div>
        </div>
      </div>





      {/* Service Sections */}
      <div className="space-y-8">
        {/* Overdue Services */}
        {data.overdue.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-red-400 mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Overdue Services ({data.overdue.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.overdue.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="high"
                />
              ))}
            </div>
          </section>
        )}

        {/* Today's Services */}
        {data.today.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-amber-400 mb-4 flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Today's Services ({data.today.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.today.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="medium"
                />
              ))}
            </div>
          </section>
        )}

        {/* Tomorrow's Services */}
        {data.tomorrow.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-cyan-400 mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Tomorrow's Services ({data.tomorrow.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.tomorrow.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="low"
                />
              ))}
            </div>
          </section>
        )}

        {/* Upcoming Services */}
        {data.upcoming.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-emerald-400 mb-4 flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              Upcoming Services (Next 7 Days) ({data.upcoming.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.upcoming.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="low"
                />
              ))}
            </div>
          </section>
        )}

        {/* Empty State */}
        {data.overdue.length === 0 && data.today.length === 0 && data.tomorrow.length === 0 && data.upcoming.length === 0 && (
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 text-slate-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-100 mb-2">No Services Scheduled</h3>
            <p className="text-slate-400 mb-6">You don&apos;t have any services scheduled for the next week.</p>
            <a
              href="/clients/new"
              className="btn-primary inline-flex items-center"
            >
              Add Client
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
