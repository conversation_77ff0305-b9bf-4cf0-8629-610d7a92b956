'use client';

import { X, CheckCircle, AlertTriangle, Info, AlertCircle } from 'lucide-react';
import { useNotification, Notification } from '@/contexts/NotificationContext';

const NotificationDisplay = () => {
  const { notifications, removeNotification } = useNotification();

  const getIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-400" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-400" />;
      default:
        return <Info className="w-5 h-5 text-gray-400" />;
    }
  };

  const getBackgroundColor = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-900/30 border-green-800/50';
      case 'error':
        return 'bg-red-900/30 border-red-800/50';
      case 'warning':
        return 'bg-amber-900/30 border-amber-800/50';
      case 'info':
        return 'bg-blue-900/30 border-blue-800/50';
      default:
        return 'bg-gray-900/30 border-gray-800/50';
    }
  };

  const getTextColor = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'text-green-100';
      case 'error':
        return 'text-red-100';
      case 'warning':
        return 'text-amber-100';
      case 'info':
        return 'text-blue-100';
      default:
        return 'text-gray-100';
    }
  };

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`border rounded-lg shadow-lg p-4 transition-all duration-300 ease-in-out transform ${getBackgroundColor(
            notification.type
          )}`}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              {getIcon(notification.type)}
            </div>
            <div className="flex-1 min-w-0">
              <h4 className={`text-sm font-medium ${getTextColor(notification.type)}`}>
                {notification.title}
              </h4>
              {notification.message && (
                <p className={`text-sm mt-1 ${getTextColor(notification.type)} opacity-90`}>
                  {notification.message}
                </p>
              )}
              {notification.action && (
                <button
                  onClick={notification.action.onClick}
                  className={`text-sm font-medium mt-2 hover:underline ${getTextColor(
                    notification.type
                  )}`}
                >
                  {notification.action.label}
                </button>
              )}
            </div>
            <button
              onClick={() => removeNotification(notification.id)}
              className={`flex-shrink-0 ml-2 p-1 rounded-md hover:bg-white hover:bg-opacity-20 transition-colors ${getTextColor(
                notification.type
              )} opacity-70 hover:opacity-100`}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotificationDisplay;