import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { generateToken, checkRateLimit, sanitizeInput } from '@/lib/auth';

// Initialize default admin user if not exists
async function initializeDefaultUser() {
  try {
    console.log('Initializing default user...');
    const dbConnection = await dbConnect();
    if (!dbConnection) {
      console.error('Database connection failed during user initialization');
      return false;
    }
    console.log('Database connected for user initialization');

    const adminExists = await (User as any).findOne({ role: 'admin' });
    console.log('Admin user exists:', !!adminExists);
    
    if (!adminExists) {
      console.log('Creating default admin user...');
      // Check if required environment variables are present
      if (!process.env.JWT_SECRET && !process.env.NEXTAUTH_SECRET) {
        console.error('JWT_SECRET or NEXTAUTH_SECRET not found in environment variables');
        return false;
      }
      
      await (User as any).createUser({
        username: 'ketan',
        password: 'radhe@1133',
        name: 'Ketan',
        role: 'admin'
      });
      console.log('✅ Default admin user created');
    } else {
      console.log('Admin user already exists');
    }
    return true;
  } catch (error) {
    console.error('Error initializing default user:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('=== LOGIN ATTEMPT STARTED ===');
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL);
    console.log('MONGODB_URI exists:', !!process.env.MONGODB_URI);
    console.log('JWT_SECRET exists:', !!process.env.JWT_SECRET);
    console.log('NEXTAUTH_SECRET exists:', !!process.env.NEXTAUTH_SECRET);
    
    // Check for critical environment variables
    if (!process.env.MONGODB_URI) {
      console.error('CRITICAL ERROR: MONGODB_URI is not set');
      return NextResponse.json(
        { success: false, error: 'Server configuration error - database not configured' },
        { status: 500 }
      );
    }
    
    if (!process.env.JWT_SECRET && !process.env.NEXTAUTH_SECRET) {
      console.error('CRITICAL ERROR: Neither JWT_SECRET nor NEXTAUTH_SECRET is set');
      return NextResponse.json(
        { success: false, error: 'Server configuration error - authentication not configured' },
        { status: 500 }
      );
    }
    
    // Initialize default user on first run
    console.log('Calling initializeDefaultUser...');
    const initSuccess = await initializeDefaultUser();
    if (!initSuccess) {
      console.error('Failed to initialize default user');
      return NextResponse.json(
        { success: false, error: 'Failed to initialize user system' },
        { status: 500 }
      );
    }
    console.log('initializeDefaultUser completed');

    const body = await request.json();
    const { username, password } = body;
    
    console.log('Login attempt for username:', username);

    // Validate required fields
    if (!username || !password) {
      console.log('Missing username or password');
      return NextResponse.json(
        { success: false, error: 'Username and password are required' },
        { status: 400 }
      );
    }

    // Sanitize inputs
    const sanitizedUsername = sanitizeInput(username);
    console.log('Sanitized username:', sanitizedUsername);

    // Check rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimit = checkRateLimit(`${clientIP}-${sanitizedUsername}`);
    console.log('Rate limit check result:', rateLimit);

    if (!rateLimit.allowed) {
      console.log('Rate limit exceeded for user:', sanitizedUsername);
      return NextResponse.json(
        {
          success: false,
          error: `Too many login attempts. Please try again in ${Math.ceil(rateLimit.blockTimeRemaining / 60)} minutes.`,
          blockTimeRemaining: rateLimit.blockTimeRemaining
        },
        { status: 429 }
      );
    }

    // Connect to database
    console.log('Connecting to database...');
    const dbConnection = await dbConnect();
    console.log('Database connection result:', !!dbConnection);
    
    if (!dbConnection) {
      console.error('Failed to connect to database');
      return NextResponse.json(
        { success: false, error: 'Database connection failed' },
        { status: 500 }
      );
    }
    console.log('Database connected successfully');

    // Find user in database
    console.log('Searching for user:', sanitizedUsername);
    const user = await (User as any).findByUsername(sanitizedUsername);
    console.log('User lookup result:', user ? 'Found' : 'Not found');
    console.log('User object keys:', user ? Object.keys(user) : 'No user');

    if (!user) {
      console.log('User not found:', sanitizedUsername);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid username or password',
          remainingAttempts: rateLimit.remainingAttempts
        },
        { status: 401 }
      );
    }

    // Verify password
    console.log('Verifying password for user:', sanitizedUsername);
    console.log('User has comparePassword method:', typeof user.comparePassword);
    
    if (typeof user.comparePassword !== 'function') {
      console.error('comparePassword method not found on user object');
      return NextResponse.json(
        { success: false, error: 'Internal server error - user validation failed' },
        { status: 500 }
      );
    }
    
    const isPasswordValid = await user.comparePassword(password);
    console.log('Password verification result:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('Invalid password for user:', sanitizedUsername);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid username or password',
          remainingAttempts: rateLimit.remainingAttempts
        },
        { status: 401 }
      );
    }

    // Check if user is active
    if (!user.isActive) {
      console.log('User account is deactivated:', sanitizedUsername);
      return NextResponse.json(
        { success: false, error: 'Account is deactivated' },
        { status: 403 }
      );
    }

    // Update last login
    console.log('Updating last login for user:', sanitizedUsername);
    user.lastLogin = new Date();
    await user.save();
    console.log('Last login updated successfully');

    // Generate secure JWT token
    console.log('Generating JWT token for user:', sanitizedUsername);
    console.log('User data for token:', {
      id: user._id.toString(),
      username: user.username,
      name: user.name,
      role: user.role,
      email: user.email
    });
    
    // Check if generateToken function exists
    console.log('generateToken function exists:', typeof generateToken);
    
    const token = generateToken({
      id: user._id.toString(),
      username: user.username,
      name: user.name,
      role: user.role,
      email: user.email
    });
    console.log('Token generated successfully');

    // Return success response with token and user data
    console.log('Login successful for user:', sanitizedUsername);
    return NextResponse.json({
      success: true,
      token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role
      }
    });

  } catch (error) {
    console.error('=== LOGIN ERROR ===');
    console.error('Error message:', error instanceof Error ? error.message : 'Unknown error');
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace available');
    console.error('Full error object:', error);
    console.error('=== END LOGIN ERROR ===');
    
    // Return a more user-friendly error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json(
      { success: false, error: 'Login failed - ' + errorMessage },
      { status: 500 }
    );
  }
}