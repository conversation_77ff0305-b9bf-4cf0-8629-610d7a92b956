// Client-side authentication utilities (no server dependencies)

export interface User {
  id: string;
  username: string;
  name: string;
  role: 'admin' | 'user';
  email?: string;
}

// Simple token validation for client-side (structure only, not cryptographic)
export const isTokenValid = (token: string): boolean => {
  if (!token) return false;
  
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return false;
    
    // Decode payload without verification (client-side only for UI purposes)
    const payload = JSON.parse(atob(parts[1] || ''));
    
    // Check if token is expired
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
};

// Get stored user data
export const getStoredUser = (): User | null => {
  if (typeof window === 'undefined') return null;

  try {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (!token || !userData) return null;

    // Basic token validation
    if (!isTokenValid(token)) {
      clearUserData();
      return null;
    }

    const user = JSON.parse(userData) as User;

    // Validate user data structure
    if (!user.id || !user.username || !user.role) {
      clearUserData();
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error getting stored user:', error);
    clearUserData();
    return null;
  }
};

// Get stored authentication token
export const getStoredToken = (): string | null => {
  if (typeof window === 'undefined') return null;

  try {
    const token = localStorage.getItem('auth_token');
    if (!token) return null;

    // Basic token validation
    if (!isTokenValid(token)) {
      clearUserData();
      return null;
    }

    return token;
  } catch (error) {
    console.error('Error getting stored token:', error);
    clearUserData();
    return null;
  }
};

// Get authorization header for API requests
export const getAuthHeader = (): { Authorization: string } | {} => {
  const token = getStoredToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Authenticated fetch wrapper
export const authenticatedFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const authHeaders = getAuthHeader();

  const config: RequestInit = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
      ...options.headers,
    },
  };

  const response = await fetch(url, config);

  // Handle 401 responses by clearing auth data and redirecting to login
  if (response.status === 401) {
    console.warn('Authentication failed, clearing session and redirecting to login');
    clearUserData();
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  }

  return response;
};

// Clear user data
export const clearUserData = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('session_created');
  } catch (error) {
    console.error('Error clearing user data:', error);
  }
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  return getStoredUser() !== null;
};

// Create session
export const createSession = (user: User, token: string): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('auth_token', token);
    localStorage.setItem('user_data', JSON.stringify(user));
    localStorage.setItem('session_created', new Date().toISOString());
  } catch (error) {
    console.error('Error creating session:', error);
    throw new Error('Failed to create session');
  }
};

// Client-side login function
export const clientLogin = async (username: string, password: string): Promise<{ success: boolean; user?: User; token?: string; error?: string }> => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    const data = await response.json();

    if (response.ok && data.success) {
      return {
        success: true,
        user: data.user,
        token: data.token
      };
    } else {
      return {
        success: false,
        error: data.error || 'Login failed'
      };
    }
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: 'Network error occurred'
    };
  }
};

// Logout function
export const logout = (): void => {
  clearUserData();
  if (typeof window !== 'undefined') {
    window.location.href = '/login';
  }
};