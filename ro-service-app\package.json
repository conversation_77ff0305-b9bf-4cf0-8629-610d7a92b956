{"name": "ro-service-app", "version": "1.0.0", "private": true, "scripts": {"dev": "node server.js", "build": "cd frontend && next build", "start": "NODE_ENV=production node server.js", "lint": "cd frontend && next lint", "lint:fix": "cd frontend && next lint --fix", "type-check": "cd frontend && tsc --noEmit", "clean": "rmdir /s /q .next 2>nul || echo Clean completed", "deploy": "npm run build && npm start", "docker:build": "docker build -t ro-service-app .", "docker:run": "docker run -p 3000:3000 ro-service-app", "docker:compose": "docker-compose up -d", "docker:down": "docker-compose down", "test": "echo 'Tests not implemented yet'", "test:watch": "echo 'Tests not implemented yet'", "security:audit": "npm audit", "security:fix": "npm audit fix", "db:init": "tsx backend/scripts/init-database.ts", "db:stats": "tsx backend/scripts/init-database.ts --stats-only", "db:seed": "tsx backend/scripts/seed-database.ts", "db:optimize": "curl -X POST http://localhost:3000/api/admin/database-stats", "db:update-admin": "node backend/scripts/update-admin-user.js", "db:reset-admin": "node backend/scripts/reset-admin.js", "db:add-test-services": "node backend/scripts/add-test-completed-services.js"}, "dependencies": {"@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.96", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "mongoose": "^8.18.1", "next": "15.5.3", "node-fetch": "^3.3.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.13", "@types/bcryptjs": "^2.4.6", "@types/dotenv": "^6.1.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tsx": "^4.20.5", "typescript": "^5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ro", "service", "management", "nextjs", "mongodb", "typescript"], "author": "RO Service Manager", "license": "MIT"}