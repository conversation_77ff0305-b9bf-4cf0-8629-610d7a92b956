'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, Plus, Users, AlertTriangle, Edit, Trash2 } from 'lucide-react';
import { formatDate } from '@/utils/utils';


interface Client {
  _id: string;
  name: string;
  phone: string;
  location: string;
  notes?: string;
  serviceType: 'recurring' | 'scheduled' | 'none';
  serviceInterval?: number;
  scheduledDate?: string;
  nextServiceDate: string;
  createdAt: string;
  updatedAt: string;
}

const ClientsPage = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  useEffect(() => {
    fetchClients();
  }, []);

  useEffect(() => {
    // Filter clients based on search term
    if (searchTerm.trim() === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client =>
        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.phone.includes(searchTerm) ||
        client.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  }, [searchTerm, clients]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      console.log('Clients page - fetching clients...');
      const response = await fetch('/api/clients');
      const result = await response.json();

      console.log('Clients page - API response:', result);

      if (result.success) {
        setClients(result.data);
        setFilteredClients(result.data);
        console.log('Clients page - clients set successfully:', {
          clientCount: result.data.length,
          sampleClients: result.data.slice(0, 3).map((c: Client) => ({
            id: c._id,
            name: c.name,
            serviceType: c.serviceType,
            nextServiceDate: c.nextServiceDate
          }))
        });
      } else {
        setError(result.error || 'Failed to fetch clients');
        console.error('Clients page - API error:', result.error);
      }
    } catch (err) {
      setError('Failed to fetch clients');
      console.error('Clients fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async (clientId: string) => {
    try {
      setDeleteLoading(clientId);
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'DELETE',
      });

      // Check if the response is ok before trying to parse JSON
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Delete request failed with status:', response.status, 'Response text:', errorText);
        throw new Error(`Failed to delete client: ${response.status} ${response.statusText}`);
      }

      // Try to parse JSON, but handle cases where it might not be JSON
      let result;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        result = await response.json();
      } else {
        const text = await response.text();
        console.warn('Response is not JSON:', text);
        result = { success: response.status === 200, message: 'Client deleted successfully' };
      }

      if (result.success) {
        // Remove client from state
        setClients(prev => prev.filter(client => client._id !== clientId));
        setShowDeleteConfirm(null);
      } else {
        setError(result.error || 'Failed to delete client');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete client';
      setError(errorMessage);
      console.error('Delete error:', err);
    } finally {
      setDeleteLoading(null);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-slate-100 mb-2">Error Loading Clients</h2>
          <p className="text-slate-400 mb-4">{error}</p>
          <button
            onClick={fetchClients}
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 px-4 sm:px-6 lg:px-8 py-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-slate-200 mb-2">Manage your RO service clients</h1>
          <Link
            href="/clients/new"
            className="btn-primary w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium text-base"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add New Client
          </Link>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search clients by name, phone, or location"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pl-12 text-base"
            />
          </div>
        </div>

        {/* Stats */}
        <div className="futuristic-card p-6 mb-6">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-indigo-400 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-slate-100">
                {filteredClients.length} {filteredClients.length === 1 ? 'Client' : 'Clients'}
              </h3>
              {searchTerm && (
                <p className="text-sm text-slate-400">
                  {filteredClients.length} of {clients.length} clients match your search
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Clients Grid */}
        {filteredClients.length > 0 ? (
          <div className="space-y-4">
            {filteredClients.map((client) => (
              <div key={client._id} className="futuristic-card p-4 card-hover">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1 min-w-0 pr-3">
                    <h3 className="text-lg font-semibold text-slate-100 mb-1 truncate">{client.name}</h3>
                    <p className="text-sm text-slate-400 mb-1">{client.phone}</p>
                    <p className="text-sm text-slate-400 line-clamp-2">{client.location}</p>
                  </div>
                  <div className="flex gap-2 flex-shrink-0">
                    <Link
                      href={`/clients/${client._id}`}
                      className="p-2 text-slate-400 hover:text-cyan-400 hover:bg-slate-700 rounded-lg transition-colors"
                      title="Edit client"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowDeleteConfirm(client._id);
                      }}
                      className="p-2 text-slate-400 hover:text-red-400 hover:bg-slate-700 rounded-lg transition-colors"
                      title="Delete client"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="border-t border-slate-600 pt-3">
                  {client.serviceType !== 'none' ? (
                    <>
                      <div className="flex justify-between items-center text-sm mb-2">
                        <span className="text-slate-400">
                          {client.serviceType === 'scheduled' ? 'Scheduled Service:' : 'Next Service:'}
                        </span>
                        <span className="font-medium text-slate-200">
                          {formatDate(client.nextServiceDate)}
                        </span>
                      </div>
                      {client.serviceType === 'recurring' && client.serviceInterval && (
                        <div className="flex justify-between items-center text-sm mb-2">
                          <span className="text-slate-400">Interval:</span>
                          <span className="text-slate-200">
                            {client.serviceInterval} {client.serviceInterval === 1 ? 'month' : 'months'}
                          </span>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="flex justify-between items-center text-sm mb-2">
                      <span className="text-slate-400">Service Status:</span>
                      <span className="font-medium text-slate-200">No Service Schedule</span>
                    </div>
                  )}
                  {client.notes && (
                    <div className="mt-2">
                      <p className="text-sm text-slate-300 line-clamp-2">{client.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            {searchTerm ? (
              <>
                <Search className="w-16 h-16 text-slate-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-200 mb-2">No clients found</h3>
                <p className="text-slate-400 mb-6">
                  No clients match your search for &quot;{searchTerm}&quot;. Try a different search term.
                </p>
                <button
                  onClick={() => setSearchTerm('')}
                  className="text-indigo-400 hover:text-indigo-300 font-medium"
                >
                  Clear search
                </button>
              </>
            ) : (
              <>
                <Users className="w-16 h-16 text-slate-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-200 mb-2">No clients yet</h3>
                <p className="text-slate-400 mb-6">
                  Get started by adding your first client to the system.
                </p>
                <Link
                  href="/clients/new"
                  className="btn-primary inline-flex items-center"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Client
                </Link>
              </>
            )}
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="futuristic-card max-w-md w-full p-6">
              <div className="flex items-center mb-4">
                <AlertTriangle className="h-6 w-6 text-red-400 mr-3" />
                <h3 className="text-lg font-semibold text-slate-100">Delete Client</h3>
              </div>
              <p className="text-slate-300 mb-6">
                Are you sure you want to delete this client? This action cannot be undone and will remove all associated service records.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleDeleteClient(showDeleteConfirm)}
                  disabled={deleteLoading === showDeleteConfirm}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
                >
                  {deleteLoading === showDeleteConfirm ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Deleting...
                    </>
                  ) : (
                    'Delete'
                  )}
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  disabled={deleteLoading === showDeleteConfirm}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientsPage;