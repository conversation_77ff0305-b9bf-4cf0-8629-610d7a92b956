'use client';

import { useAuth } from '@/components/AuthContext';
import LoadingSpinner from '@/components/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner fullScreen size="xl" text="Loading..." />;
  }

  if (!isAuthenticated) {
    return <LoadingSpinner fullScreen size="xl" text="Redirecting to login..." />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;