import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Client from '@/models/Client';
import mongoose from 'mongoose';

export async function GET(
  _request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    // Resolve the params promise
    const params = await context.params;

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid client ID' },
        { status: 400 }
      );
    }
    
    const client = await (Client as any).findById(params.id);
    
    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: client
    });
  } catch (error) {
    console.error('Error fetching client:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch client' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    // Resolve the params promise
    const params = await context.params;

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid client ID' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    const { name, phone, location, notes, serviceType, serviceInterval, scheduledDate } = body;

    // Validate service type specific fields
    if (serviceType === 'recurring' && !serviceInterval) {
      return NextResponse.json(
        { success: false, error: 'Service interval is required for recurring services' },
        { status: 400 }
      );
    }

    if (serviceType === 'scheduled' && !scheduledDate) {
      return NextResponse.json(
        { success: false, error: 'Scheduled date is required for scheduled services' },
        { status: 400 }
      );
    }

    const updateData: any = {
      name,
      phone,
      location,
      notes,
      serviceType: serviceType || 'recurring'
    };

    console.log('Client update - received data:', { name, phone, location, serviceType, serviceInterval, scheduledDate });

    if (serviceType === 'scheduled') {
      // Normalize the scheduled date to UTC date without time
      const schedDate = new Date(scheduledDate);
      updateData.scheduledDate = new Date(Date.UTC(schedDate.getFullYear(), schedDate.getMonth(), schedDate.getDate()));
      // Clear serviceInterval for scheduled services
      updateData.serviceInterval = null;
      console.log('Client update - scheduled service data:', { scheduledDate: updateData.scheduledDate });
    } else {
      updateData.serviceInterval = serviceInterval || 3;
      // Clear scheduledDate for recurring services
      updateData.scheduledDate = null;
      console.log('Client update - recurring service data:', { serviceInterval: updateData.serviceInterval });
    }

    // Find the client first to ensure it exists
    const client = await (Client as any).findById(params.id);

    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    // Update the client properties
    Object.assign(client, updateData);

    // Save the client to trigger pre-validate hook for nextServiceDate calculation
    await client.save();

    console.log('Client update - saved client:', {
      id: client._id,
      serviceType: client.serviceType,
      nextServiceDate: client.nextServiceDate
    });

    return NextResponse.json({
      success: true,
      data: client
    });
  } catch (error) {
    console.error('Error updating client:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update client' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    // Resolve the params promise
    const params = await context.params;

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid client ID' },
        { status: 400 }
      );
    }
    
    const client = await (Client as any).findByIdAndDelete(params.id);
    
    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Client deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting client:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete client' },
      { status: 500 }
    );
  }
}