'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Phone, MapPin, Calendar, CheckCircle, Clock, Edit } from 'lucide-react';
import { formatDate, formatPhoneNumber, getServiceStatus } from '../utils/utils';
import { useNotification } from '@/contexts/NotificationContext';
import ServiceCompletionModal from './ServiceCompletionModal';

interface ClientCardProps {
  client: {
    _id: string;
    name: string;
    phone: string;
    location: string;
    notes?: string;
    serviceType: 'recurring' | 'scheduled' | 'none';
    serviceInterval?: number;
    scheduledDate?: string;
    nextServiceDate: string;
  };
  onServiceComplete?: (clientId: string) => void;
  priority?: 'high' | 'medium' | 'low';
  showCompleteButton?: boolean;
}

const ClientCard = ({
  client,
  onServiceComplete,
  priority = 'low',
  showCompleteButton = true
}: ClientCardProps) => {
  const router = useRouter();
  const { showSuccess, showError } = useNotification();
  const [completing, setCompleting] = useState(false);
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Only calculate service status if client has a service schedule
  const serviceStatus = client.serviceType !== 'none' ? getServiceStatus(new Date(client.nextServiceDate)) : null;

  const handleCompleteService = async () => {
    // Show the completion modal instead of directly completing
    setShowCompletionModal(true);
  };

  const handleCompleteOnly = async () => {
    if (!onServiceComplete) return;

    try {
      setCompleting(true);
      setError(null);

      console.log('ClientCard - Starting service completion for client:', client._id);

      // Create a service record and mark it as completed
      const serviceResponse = await fetch('/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId: client._id,
          serviceDate: new Date().toISOString(),
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!serviceResponse.ok) {
        const errorData = await serviceResponse.json();
        throw new Error(errorData.error || 'Failed to create service record');
      }

      const serviceResult = await serviceResponse.json();
      console.log('ClientCard - Service record created:', serviceResult.data._id);

      // Complete the service
      const completeResponse = await fetch(`/api/services/${serviceResult.data._id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!completeResponse.ok) {
        const errorData = await completeResponse.json();
        throw new Error(errorData.error || 'Failed to complete service');
      }

      const completeResult = await completeResponse.json();
      console.log('ClientCard - Service completed successfully:', completeResult);

      // Close the modal
      setShowCompletionModal(false);

      // Show success notification
      showSuccess('Service Completed', `Service for ${client.name} has been completed successfully.`);

      // Call the parent callback to refresh data
      onServiceComplete(client._id);

    } catch (error) {
      console.error('Error completing service:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete service. Please try again.';
      setError(errorMessage);
      showError('Service Completion Failed', errorMessage);
    } finally {
      setCompleting(false);
    }
  };

  const handleConvertToRecurring = async () => {
    if (!onServiceComplete) return;

    try {
      setCompleting(true);
      setError(null);

      console.log('ClientCard - Starting service completion for client:', client._id);

      // Create a service record and mark it as completed
      const serviceResponse = await fetch('/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId: client._id,
          serviceDate: new Date().toISOString(),
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!serviceResponse.ok) {
        const errorData = await serviceResponse.json();
        throw new Error(errorData.error || 'Failed to create service record');
      }

      const serviceResult = await serviceResponse.json();
      console.log('ClientCard - Service record created:', serviceResult.data._id);

      // Complete the service
      const completeResponse = await fetch(`/api/services/${serviceResult.data._id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!completeResponse.ok) {
        const errorData = await completeResponse.json();
        throw new Error(errorData.error || 'Failed to complete service');
      }

      const completeResult = await completeResponse.json();
      console.log('ClientCard - Service completed successfully:', completeResult);

      // Convert client to recurring service using the new dedicated endpoint
      const convertToRecurringResponse = await fetch(`/api/clients/${client._id}/convert-to-recurring`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!convertToRecurringResponse.ok) {
        const errorData = await convertToRecurringResponse.json();
        throw new Error(errorData.error || 'Failed to convert client to recurring service');
      }

      const convertToRecurringResult = await convertToRecurringResponse.json();
      console.log('ClientCard - Client converted to recurring service:', convertToRecurringResult);

      // Close the modal
      setShowCompletionModal(false);

      // Show success notification
      showSuccess('Service Completed', `Service for ${client.name} has been completed and converted to 3-month recurring service.`);

      // Call the parent callback to refresh data
      onServiceComplete(client._id);

    } catch (error) {
      console.error('Error completing service:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete service. Please try again.';
      setError(errorMessage);
      showError('Service Completion Failed', errorMessage);
    } finally {
      setCompleting(false);
    }
  };

  const handleEditClient = () => {
    router.push(`/clients/${client._id}`);
  };

  const getPriorityBorder = () => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-l-red-500';
      case 'medium':
        return 'border-l-4 border-l-orange-500';
      default:
        return 'border-l-4 border-l-blue-500';
    }
  };

  return (
    <div className={`futuristic-card card-hover ${getPriorityBorder()}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-slate-100 mb-1">{client.name}</h3>
            {client.serviceType !== 'none' && serviceStatus && (
              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                serviceStatus === 'overdue' ? 'status-overdue' :
                serviceStatus === 'today' ? 'status-today' :
                serviceStatus === 'upcoming' ? 'status-upcoming' :
                'status-upcoming'
              }`}>
                <Clock className="w-3 h-3 mr-1" />
                {serviceStatus === 'overdue' && 'Overdue'}
                {serviceStatus === 'today' && 'Due Today'}
                {serviceStatus === 'tomorrow' && 'Due Tomorrow'}
                {serviceStatus === 'upcoming' && 'Upcoming'}
              </div>
            )}
            {client.serviceType === 'none' && (
              <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
                No Service Schedule
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            {showCompleteButton && serviceStatus && (serviceStatus === 'overdue' || serviceStatus === 'today') && (
              <button
                onClick={handleCompleteService}
                disabled={completing}
                className="btn-success inline-flex items-center justify-center w-10 h-10 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                title="Complete Service"
              >
                {completing ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <CheckCircle className="w-4 h-4" />
                )}
              </button>
            )}
            <button
              onClick={handleEditClient}
              className="btn-edit inline-flex items-center justify-center w-10 h-10 rounded-lg text-sm"
              title="Edit Client"
            >
              <Edit className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Contact Info */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center text-slate-400">
            <Phone className="w-4 h-4 mr-3 flex-shrink-0" />
            <a 
              href={`tel:${client.phone}`}
              className="text-indigo-400 hover:text-indigo-300 transition-colors"
            >
              {formatPhoneNumber(client.phone)}
            </a>
          </div>
          
          <div className="flex items-start text-slate-400">
            <MapPin className="w-4 h-4 mr-3 flex-shrink-0 mt-0.5" />
            <span className="text-sm">{client.location}</span>
          </div>
          
          {client.serviceType !== 'none' && (
            <div className="flex items-center text-slate-400">
              <Calendar className="w-4 h-4 mr-3 flex-shrink-0" />
              <span className="text-sm">
                {client.serviceType === 'scheduled' ? 'Scheduled Service:' : 'Next Service:'} {formatDate(client.nextServiceDate)}
              </span>
            </div>
          )}
        </div>

        {/* Service Type Info */}
        {client.serviceType !== 'none' && (
          <div className="text-sm text-slate-500 mb-4">
            {client.serviceType === 'scheduled' ? (
              <span>One-time scheduled service</span>
            ) : (
              <span>
                Recurring: {client.serviceInterval} {client.serviceInterval === 1 ? 'month' : 'months'}
              </span>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-md p-3 mb-4">
            <p className="text-sm text-red-400">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-xs text-red-300 hover:text-red-200 mt-1"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Notes */}
        {client.notes && (
          <div className="bg-slate-800/50 rounded-md p-3">
            <p className="text-sm text-slate-300">{client.notes}</p>
          </div>
        )}
      </div>

      {/* Service Completion Modal */}
      {showCompletionModal && (
        <ServiceCompletionModal
          isOpen={showCompletionModal}
          clientName={client.name}
          clientId={client._id}
          onClose={() => setShowCompletionModal(false)}
          onCompleteOnly={handleCompleteOnly}
          onConvertToRecurring={handleConvertToRecurring}
        />
      )}
    </div>
  );
};

export default ClientCard;