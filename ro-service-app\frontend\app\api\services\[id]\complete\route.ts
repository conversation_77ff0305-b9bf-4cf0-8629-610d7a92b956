import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Service from '@/models/Service';
import Client from '@/models/Client';
import mongoose from 'mongoose';

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    // Resolve the params promise
    const params = await context.params;
    const { id } = params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid service ID' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    const { notes } = body;
    
    // Find the service
    const service = await (Service as any).findById(id);
    if (!service) {
      return NextResponse.json(
        { success: false, error: 'Service not found' },
        { status: 404 }
      );
    }
    
    // Update service status to completed
    service.status = 'completed';
    service.notes = notes || service.notes;
    service.completedAt = new Date();
    await service.save();
    
    // Update client's last service date and calculate next service date
    const client = await (Client as any).findById(service.clientId);
    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    console.log('Service completion - updating client:', {
      clientId: client._id,
      oldLastServiceDate: client.lastServiceDate,
      newLastServiceDate: service.serviceDate,
      serviceType: client.serviceType
    });

    // Prepare update data
    const updateData: any = {
      lastServiceDate: service.serviceDate
    };

    // For scheduled services, set nextServiceDate far in the future to remove from dashboard
    // For recurring services, calculate the next service date
    if (client.serviceType === 'scheduled') {
      updateData.nextServiceDate = new Date('2099-12-31');
      console.log('Scheduled service completed - setting nextServiceDate to far future');
    } else {
      // For recurring services, calculate the next service date based on completion date + interval
      const completionDate = new Date(service.serviceDate);
      const nextDate = new Date(Date.UTC(
        completionDate.getFullYear(), 
        completionDate.getMonth(), 
        completionDate.getDate()
      ));
      const interval = client.serviceInterval || 3;
      nextDate.setUTCMonth(nextDate.getUTCMonth() + interval);
      updateData.nextServiceDate = nextDate;
      
      console.log('Recurring service completed - calculated new nextServiceDate:', {
        completionDate: service.serviceDate,
        interval,
        newNextServiceDate: nextDate
      });
    }
    
    // Use findByIdAndUpdate to bypass pre-validate hooks
    const updatedClient = await (Client as any).findByIdAndUpdate(
      client._id,
      updateData,
      { new: true, runValidators: false }
    );

    if (!updatedClient) {
      throw new Error('Failed to update client');
    }

    console.log('Service completion - client updated:', {
      clientId: updatedClient._id,
      newLastServiceDate: updatedClient.lastServiceDate,
      newNextServiceDate: updatedClient.nextServiceDate
    });

    return NextResponse.json({
      success: true,
      data: {
        service,
        client: {
          _id: updatedClient._id,
          name: updatedClient.name,
          phone: updatedClient.phone,
          location: updatedClient.location,
          notes: updatedClient.notes,
          serviceType: updatedClient.serviceType,
          serviceInterval: updatedClient.serviceInterval,
          scheduledDate: updatedClient.scheduledDate,
          lastServiceDate: updatedClient.lastServiceDate,
          nextServiceDate: updatedClient.nextServiceDate,
          createdAt: updatedClient.createdAt,
          updatedAt: updatedClient.updatedAt
        }
      },
      message: 'Service completed successfully'
    });
  } catch (error) {
    console.error('Error completing service:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to complete service' },
      { status: 500 }
    );
  }
}