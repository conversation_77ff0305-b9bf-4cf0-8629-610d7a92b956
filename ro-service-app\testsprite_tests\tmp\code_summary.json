{"tech_stack": ["JavaScript", "TypeScript", "Next.js", "React", "Node.js", "MongoDB", "Tailwind CSS"], "features": [{"name": "Client Management", "description": "CRUD operations for managing clients", "files": ["frontend/app/clients/page.tsx", "frontend/components/ClientCard.tsx", "backend/models/Client.js", "backend/routes/api/clients/index.js"]}, {"name": "Service Management", "description": "Management of services for clients", "files": ["frontend/app/services/page.tsx", "frontend/components/ServiceCard.tsx", "backend/models/Service.js", "backend/routes/api/services/index.js"]}, {"name": "Authentication", "description": "User authentication and authorization", "files": ["frontend/components/LoginPage.tsx", "frontend/lib/auth.ts", "backend/lib/auth.ts", "backend/models/User.ts", "backend/routes/api/auth/login.js"]}, {"name": "Dashboard", "description": "Main dashboard displaying key metrics and information", "files": ["frontend/app/page.tsx", "frontend/components/Dashboard.tsx", "backend/routes/api/dashboard/index.js"]}, {"name": "Database Operations", "description": "Core database operations and connection management", "files": ["backend/lib/mongodb.js", "backend/utils/mongodb.js", "frontend/lib/mongodb.js"]}]}