
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TestSprite AI Project Test Report</title>
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMjAiIGZpbGw9IndoaXRlIi8+CjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMF8zMDBfMzc3MzApIj4KPHBhdGggZD0iTTMzLjI1NDkgNi4wODgyN0MzMy4yNjE3IDYuMDg4MjcgMzMuMjU3MiA2LjA5OTY4IDMzLjI0MTIgNi4xMjE0OEMzMy4xMTIgNi43NDc1NyAzMy4xOTQ2IDE1Ljk4NjggMzMuMjU0OSAyMC42ODY5TDM3LjIzMjQgMjQuODA1MUwzNi4yOTg4IDI1LjE0M0MzMC4xNzE2IDI3LjM2MzQgMjQuNDk1MyAzMC42NzI4IDE5LjU0NDkgMzQuOTExNUMxNC42NDE2IDMwLjY3MzQgOS4wMDgyNyAyNy4zNjI2IDIuOTE5OTIgMjUuMTQxTDEuOTk5MDIgMjQuODA1MUw2LjAyMjQ2IDIwLjY4NjlWNi4wODgyN0wxNC4zMDQ3IDE0LjM3MDVIMjQuOTczNkMyNy42NjgzIDExLjczNTQgMzIuOTAwNyA2LjU4NDc5IDMzLjI0MTIgNi4xMjE0OEMzMy4yNDU2IDYuMTAwMTMgMzMuMjQ5OSA2LjA4ODgxIDMzLjI1NDkgNi4wODgyN1pNMjcuMjEzOSAxNi45OTA2QzIzLjk4OTEgMTguOTE0NyAyMS4wNTkxIDIxLjI5NDQgMTguNTE0NiAyNC4wNTZMMTUuMDk4NiAyMS4yNDg0TDEzLjY5NTMgMjIuNDE4NEwxOS4zNTY0IDI4LjgyODVDMjEuNzE0IDI0LjczMzkgMjQuNjcwOSAyMS4wMTUgMjguMTI3OSAxNy43OTQzTDI4LjM4NzcgMTcuNTUyMUwyNy45MTk5IDE2LjU2OTdMMjcuMjEzOSAxNi45OTA2WiIgZmlsbD0iIzA5MDkwOSIvPgo8L2c+CjxkZWZzPgo8Y2xpcFBhdGggaWQ9ImNsaXAwXzMwMF8zNzczMCI+CjxyZWN0IHdpZHRoPSIzNSIgaGVpZ2h0PSIzNSIgZmlsbD0id2hpdGUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDIgMykiLz4KPC9jbGlwUGF0aD4KPC9kZWZzPgo8L3N2Zz4K">
  <style>
@import url('https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css');
@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap');

/* Custom CSS Variables for Design System */
:root {
  --fg-highlighted: #19c379;
  --fg-default: #f7f7f8;
  --fg-muted: #e4e4e4;
  --fg-secondary: #959597;
  --fg-primary: #f7f7f8;
  --fg-success: #19c379;
  --fg-error: #ef4146;
  
  --bg-default: #000000;
  --bg-default-rgb: 9, 9, 9;
  --bg-lifted: #101011;
  --bg-elevated: #17181A;
  --bg-success: #10a36333;
  --bg-error: #c2353933;
  
  --border-default: #17181A;
  --border-elevated: #212224;
  --border-primary: #10a363;
  
  --interactive-bg-default: #212224;
  --interactive-bg-primary: #1a7f53;
  --interactive-fg-primary: #ffffff;
  
  --logo-icon-color: #19c379;
  --logo-text-color: #f7f7f8;
  
  /* Status tag colors */
  --status-success-bg: rgba(16, 163, 99, 0.2);
  --status-success-border: rgba(16, 163, 99, 0.2);
  --status-success-text: #93E6C2;
  --status-warning-bg: rgba(183, 112, 28, 0.2);
  --status-warning-border: rgba(183, 112, 28, 0.2);
  --status-warning-text: #F4C47D;
  --status-error-bg: rgba(194, 53, 57, 0.2);
  --status-error-border: rgba(194, 53, 57, 0.2);
  --status-error-text: #FFB3B3;
  
  --border-radius-medium: 6px;
  --border-radius-large: 8px;
}

/* Light Theme Variables */
[data-theme="light"] {
  --fg-highlighted: #10a363;
  --fg-default: #090909;
  --fg-muted: #6b6b6d;
  --fg-secondary: #88888B;
  --fg-primary: #f7f7f8;
  --fg-success: #10a363;
  --fg-error: #c23539;
  
  --bg-default: #ffffff;
  --bg-default-rgb: 255, 255, 255;
  --bg-lifted: #FAFAFA;
  --bg-elevated: #ffffff;
  --bg-success: #10a36333;
  --bg-error: #c2353933;
  
  --border-default: #eeeeef;
  --border-elevated: #eaeaeb;
  --border-primary: #1a7f53;
  
  --interactive-bg-default: #ffffff;
  --interactive-bg-primary: #10a363;
  --interactive-fg-primary: #ffffff;
  
  --logo-icon-color: #10a363;
  --logo-text-color: #090909;
  
  /* Status tag colors */
  --status-success-bg: rgba(16, 163, 99, 0.1);
  --status-success-border: rgba(16, 163, 99, 0.15);
  --status-success-text: #10A363;
  --status-warning-bg: rgba(183, 112, 28, 0.1);
  --status-warning-border: rgba(183, 112, 28, 0.15);
  --status-warning-text: #B7701C;
  --status-error-bg: rgba(194, 53, 57, 0.1);
  --status-error-border: rgba(194, 53, 57, 0.15);
  --status-error-text: #C23539;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Geist', sans-serif;
  background-color: var(--bg-default);
  color: var(--fg-default);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  padding-top: 72px; /* Account for fixed header height */
}

/* Layout Components */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(var(--bg-default-rgb), 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-default);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 12px 60px;
  width: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.logo-image {
  width: auto;
}

.logo-link {
  display: inline-block;
  text-decoration: none;
}

.logo-divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-elevated);
  margin: 0 8px;
}

.logo-text {
  font-size: 20px;
  font-weight: 550;
  letter-spacing: -0.02em;
}

.logo-text-test {
  color: var(--fg-default);
}

.logo-text-report {
  color: var(--fg-highlighted);
}

/* Theme Toggle Button */
.theme-toggle {
  background: var(--bg-lifted);
  border: 1px solid var(--border-default);
  border-radius: var(--border-radius-medium);
  padding: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  transition: all 0.2s ease;
  position: relative;
}

.theme-toggle:hover {
  background: var(--border-default);
  border-color: var(--border-elevated);
}

.theme-icon {
  transition: all 0.2s ease;
  position: absolute;
}

.sun-icon {
  opacity: 0;
  transform: rotate(180deg);
}

.moon-icon {
  opacity: 1;
  transform: rotate(0deg);
}

/* Light theme toggle states */
[data-theme="light"] .sun-icon {
  opacity: 1;
  transform: rotate(0deg);
}

[data-theme="light"] .moon-icon {
  opacity: 0;
  transform: rotate(-180deg);
}

.main-content {
  padding: 90px 60px;
  display: flex;
  justify-content: center;
  gap: 120px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.content-section {
  flex: 1;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.sidebar {
  position: sticky;
  top: 120px; /* Stick below the fixed header with additional spacing when scrolling */
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 260px;
  height: fit-content;
  max-height: calc(100vh - 92px); /* Don't exceed viewport height minus header and spacing */
  overflow-y: auto; /* Allow scrolling if content is too long */
}

/* Typography */
.title-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-main {
  font-family: 'Geist', sans-serif;
  font-weight: 550;
  font-size: 36px;
  line-height: 42px;
  letter-spacing: -0.4px;
  color: var(--fg-default);
  margin: 0;
}

.title-subtitle {
  font-family: 'Geist', sans-serif;
  font-weight: 400;
  font-size: 18px;
  line-height: 26px;
  letter-spacing: -0.18px;
  color: var(--fg-secondary);
  margin: 0;
}

.section-subtitle {
  font-family: 'Geist', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.16px;
  color: var(--fg-secondary);
  margin: 0;
}

.title-section {
  font-family: 'Geist', sans-serif;
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: -0.3px;
  color: var(--fg-default);
  margin: 0;
}

.title-requirement {
  font-family: 'Geist', sans-serif;
  font-weight:480;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: -0.2px;
  color: var(--fg-default);
  margin: 0;
}

.title-requirement::before {
  counter-increment: accordion-counter;
  content: counter(accordion-counter) ". ";
  color: var(--fg-secondary);
  margin-right: 4px;
}

.text-body {
  font-family: 'Geist', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.16px;
  color: var(--fg-default);
  margin: 0;
}

.text-body-medium {
  font-family: 'Geist', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.16px;
  color: var(--fg-secondary);
  margin: 0;
}

.text-link {
  color: var(--fg-default);
  text-decoration: underline;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  transition: color 0.2s ease;
}

.text-link:hover {
  color: var(--fg-secondary);
}

/* Metadata Section */
.metadata-section {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.metadata-container {
  padding: 0px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metadata-row {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.metadata-label {
  width: 200px;
  flex-shrink: 0;
}

.metadata-value {
  flex: 1;
}

/* Divider */
.divider {
  height: 1px;
  background-color: var(--border-default);
  width: 100%;
}

/* Requirements Section */
.requirements-section {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.requirement-wrapper {
  display: flex;
  flex-direction: column;
}

.requirement-description {
  color: var(--fg-secondary);
}

/* Accordion Styles */
.accordion-container {
  border: 1px solid var(--border-default);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  counter-reset: accordion-counter;
}

.accordion-item {
  border: none;
  border-radius: 0;
  overflow: hidden;
}

.accordion-item:not(:last-child) {
  border-bottom: 1px solid var(--border-default);
}

.accordion-header {
  padding: 16px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
  background-color: var(--bg-lifted);
  transition: background-color 0.2s ease;
}

.requirement-content {
  flex: 1;
}

.requirement-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.requirement-status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px 2px 2px;
  border-radius: var(--border-radius-medium);
  font-family: 'Geist', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: -0.24px;
  flex-shrink: 0;
}

.requirement-description {
  margin: 0;
}

.requirement-status-tag.status-success {
  background-color: var(--status-success-bg);
  border: 1px solid var(--status-success-border);
  color: var(--status-success-text);
}

.requirement-status-tag.status-warning {
  background-color: var(--status-warning-bg);
  border: 1px solid var(--status-warning-border);
  color: var(--status-warning-text);
}

.requirement-status-tag.status-error {
  background-color: var(--status-error-bg);
  border: 1px solid var(--status-error-border);
  color: var(--status-error-text);
}

.accordion-header:hover {
  background-color: var(--bg-elevated);
}

/* Light mode specific hover - darker than bg-lifted */
[data-theme="light"] .accordion-header:hover {
  background-color: #F0F0F0;
}

.accordion-icon {
  flex-shrink: 0;
  transition: transform 0.3s ease;
  margin-top: 2px;
}

.accordion-item.active .accordion-icon {
  transform: rotate(180deg);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  background-color: var(--bg-lifted);
}

.accordion-item.active .accordion-content {
  max-height: 100%; /* Arbitrary large value to allow full expansion */
  transition: max-height 0.5s ease-in;
}

.accordion-content .test-wrapper {
  padding: 16px;
  gap: 16px;
  display: flex;
  flex-direction: column;
}

/* Test Cards */
.test-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-card {
  background-color: var(--bg-lifted);
  border: 1px solid var(--border-elevated);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.test-header {
  background-color: var(--bg-elevated);
  border-bottom: 1px solid var(--border-elevated);
  padding: 10px 16px;
}

.test-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-field {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.test-field-label {
  width: 200px;
  flex-shrink: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.test-field-value {
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  min-width: 0;
}

/* Status Components */
.status-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  width: 18px;
  height: 18px;
}

.status-pass {
  color: var(--fg-success);
}

.status-fail {
  color: var(--fg-error);
}

/* Coverage Metrics */
.coverage-section {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.coverage-summary {
  background-color: var(--bg-lifted);
  border: 1px solid var(--border-default);
  border-radius: var(--border-radius-large);
  padding: 16px;
}

.coverage-list {
  list-style: disc;
  list-style-position: outside;
  margin-left: 21px;
  color: var(--fg-secondary);
}

.coverage-list li {
  padding-left: 12px;
  margin-bottom: 12px;
}

.coverage-list li:last-child {
  margin-bottom: 0;
}

.coverage-highlight {
  color: var(--fg-default);
  font-weight: 500;
}

/* Data Table */
.data-table {
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.table-header {
  background-color: var(--bg-default);
  display: flex;
  border-bottom: 1px solid var(--border-elevated);
}

.table-header-cell {
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  min-width: 100px;
  color: var(--fg-default);
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.14px;
}

.table-header-cell:first-child {
  flex: 3;
}

.table-rows {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: flex;
  border-bottom: 1px solid var(--border-default);
  transition: background-color 0.2s ease;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: var(--bg-lifted);
}

.table-cell {
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  min-width: 100px;
  background-color: var(--bg-default);
  color: var(--fg-secondary);
}

.table-cell:first-child {
  flex: 3;
  color: var(--fg-default);
  font-weight: 500;
}

.table-cell .text-body {
  color: var(--fg-secondary);
}

.table-cell:first-child .text-body {
  color: var(--fg-default);
  font-weight: 500;
}

.table-header-cell .text-body-medium {
  color: var(--fg-default);
  font-weight: 500;
}

/* Footer */
.footer {
  background-color: var(--bg-default);
  border-top: 1px solid var(--border-default);
  margin-top: 80px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 60px 60px 60px;
  width: 100%;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  gap: 80px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex-shrink: 0;
  align-items: flex-start;
}

.footer-logo {
  width: auto;
  margin-left: -8px;
}

.footer-copyright {
  font-family: 'Geist', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--fg-secondary);
  margin: 0;
  margin-left: -8px;
}

.footer-links {
  display: flex;
  gap: 60px;
}

.footer-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 140px;
}

.footer-column-title {
  font-family: 'Geist', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--fg-default);
  margin: 0;
}

.footer-link-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.footer-link {
  font-family: 'Geist', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--fg-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--fg-default);
}

/* Footer responsive adjustments for larger screens */
@media (max-width: 1200px) {
  .footer-content {
    flex-direction: column;
    gap: 40px;
  }
  
  .footer-links {
    gap: 40px;
  }
}

@media (max-width: 900px) {
  .footer-content {
    flex-direction: column;
    gap: 30px;
  }
  
  .footer-links {
    gap: 30px;
  }
  
  .footer-column {
    min-width: 130px;
  }
}

/* References Section */
.references-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Sidebar */
.sidebar-title {
  padding: 0;
  color: var(--fg-muted);
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sidebar-title-icon {
  color: var(--fg-secondary);
  flex-shrink: 0;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 2px;
  position: relative;
}

.sidebar-nav::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--border-default);
  z-index: 0;
}

.sidebar-nav-item {
  padding: 6px 16px;
  color: var(--fg-secondary);
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.14px;
  text-decoration: none;
  display: block;
  cursor: pointer;
  position: relative;
  border-left: 2px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.sidebar-nav-item.active {
  color: var(--fg-highlighted);
  border-left: 2px solid var(--fg-highlighted);
  background-color: transparent;
  font-size: 14.5px;
  font-weight: 400;
}

.sidebar-nav-item:hover:not(.active) {
  color: var(--fg-default);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 30px;
    padding: 40px 20px;
    align-items: center;
  }
  
  .content-section {
    width: 100%;
    max-width: 800px;
  }
  
  .sidebar {
    order: -1;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
  }
  
  .sidebar-nav {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .sidebar-nav-item {
    flex: 1;
    min-width: 200px;
    text-align: center;
  }
}

@media (max-width: 1200px) {
  .sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 12px 20px;
  }
  
  .main-content {
    padding: 30px 20px;
  }
  
  .title-main {
    font-size: 30px;
    line-height: 36px;
  }
  
  .title-section {
    font-size: 20px;
    line-height: 28px;
  }
  
  .metadata-row {
    flex-direction: column;
    gap: 4px;
  }
  
  .metadata-label {
    width: auto;
  }
  
  .test-field {
    flex-direction: column;
    gap: 4px;
  }
  
  .test-field-label {
    width: auto;
  }
  
  .table-header-cell,
  .table-cell {
    padding: 8px 4px;
    font-size: 14px;
    min-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .table-header-cell:first-child,
  .table-cell:first-child {
    flex: 2;
    padding-right: 8px;
    white-space: normal;
    overflow: visible;
  }
  
  .table-header-cell:not(:first-child),
  .table-cell:not(:first-child) {
    flex: 1;
    text-align: center;
  }
  
  .footer-container {
    padding: 40px 20px;
    max-width: 100%;
    box-sizing: border-box;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 30px;
  }
  
  .footer-links {
    flex-wrap: wrap;
    gap: 20px;
    justify-content: flex-start;
  }
  
  .footer-column {
    min-width: 120px;
    max-width: 150px;
    flex: 0 0 auto;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .logo-section {
    justify-content: center;
  }
  
  .theme-toggle {
    position: absolute;
    top: 16px;
    right: 16px;
  }
  
  .open-app-btn {
    align-self: center;
  }
  
  .sidebar-nav {
    flex-direction: column;
  }
  
  .sidebar-nav-item {
    min-width: auto;
    text-align: left;
  }
}
  </style>
    </head>
    <body>
  <!-- Header Section -->
  <header class="header">
    <div class="header-container">
      <div class="header-content">
    <div class="logo-section">
          <a href="https://www.testsprite.com/" target="_blank" class="logo-link">
            <svg class="logo-image" width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_header_logo)">
                <path d="M8.4375 7.79639H15.7529C17.5998 5.9903 21.1854 2.46169 21.4219 2.14111C21.3334 2.57479 21.3913 8.90681 21.4326 12.1284L24.1592 14.9517L23.5186 15.1841C19.3172 16.7066 15.4256 18.9759 12.0312 21.8823C8.66893 18.9761 4.80584 16.7056 0.630859 15.1821L-0.000976562 14.9517L2.75879 12.1284V2.11768L8.4375 7.79639ZM17.29 9.59326C15.0787 10.9127 13.0691 12.5452 11.3242 14.439L8.98242 12.5132L8.01953 13.3159L11.9023 17.7114C13.5189 14.9037 15.5464 12.3535 17.917 10.145L18.0947 9.979L17.7734 9.30518L17.29 9.59326Z" fill="var(--fg-default)"/>
              </g>
        <defs>
                <clipPath id="clip0_header_logo">
            <rect width="24" height="24" fill="white"/>
          </clipPath>
        </defs>
      </svg>
          </a>
          <div class="logo-divider"></div>
          <span class="logo-text">
            <span class="logo-text-test">Test</span>
            <span class="logo-text-report">Report</span>
          </span>
    </div>
    
    <!-- Theme Toggle Button -->
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle theme">
          <svg class="theme-icon sun-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_sun)">
              <path d="M8 12C8 13.0609 8.42143 14.0783 9.17157 14.8284C9.92172 15.5786 10.9391 16 12 16C13.0609 16 14.0783 15.5786 14.8284 14.8284C15.5786 14.0783 16 13.0609 16 12C16 10.9391 15.5786 9.92172 14.8284 9.17157C14.0783 8.42143 13.0609 8 12 8C10.9391 8 9.92172 8.42143 9.17157 9.17157C8.42143 9.92172 8 10.9391 8 12Z" stroke="var(--fg-secondary)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3 12H4M12 3V4M20 12H21M12 20V21M5.6 5.6L6.3 6.3M18.4 5.6L17.7 6.3M17.7 17.7L18.4 18.4M6.3 17.7L5.6 18.4" stroke="var(--fg-secondary)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </g>
            <defs>
              <clipPath id="clip0_sun">
                <rect width="24" height="24" fill="white"/>
              </clipPath>
            </defs>
      </svg>
          <svg class="theme-icon moon-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_moon)">
              <path d="M11.9998 2.99994C12.1318 2.99994 12.2628 2.99994 12.3928 2.99994C11.1081 4.19365 10.2824 5.79979 10.0591 7.53916C9.83577 9.27854 10.229 11.0412 11.1705 12.5207C12.112 14.0002 13.5422 15.103 15.2124 15.6374C16.8826 16.1718 18.6873 16.1041 20.3128 15.4459C19.6875 16.9504 18.6656 18.257 17.356 19.2262C16.0464 20.1954 14.4982 20.791 12.8767 20.9493C11.2552 21.1077 9.62104 20.8229 8.14867 20.1253C6.6763 19.4278 5.42089 18.3436 4.51637 16.9885C3.61185 15.6334 3.09213 14.0582 3.01267 12.4309C2.9332 10.8036 3.29696 9.18524 4.06515 7.74846C4.83334 6.31167 5.97714 5.11037 7.37454 4.27268C8.77195 3.43499 10.3705 2.99234 11.9998 2.99194V2.99994Z" stroke="var(--fg-secondary)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </g>
            <defs>
              <clipPath id="clip0_moon">
                <rect width="24" height="24" fill="white"/>
              </clipPath>
            </defs>
      </svg>
    </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="main-content">
    <div class="content-section">
      <!-- Metadata Section -->
      <div id="metadata" class="metadata-section">
        <!-- Title -->
        <div class="title-group">
          <h1 class="title-main">TestSprite AI Test Report</h1>
          <p class="title-subtitle">Generated automatically by TestSprite AI Agent – providing detailed test results.</p>
        </div>
        <div class="metadata-container">
        <div class="metadata-row">
          <div class="metadata-label text-body-medium">Project Name</div>
          <div class="metadata-value text-body">RO Service Manager</div>
        </div>
        <div class="metadata-row">
          <div class="metadata-label text-body-medium">Date</div>
          <div class="metadata-value text-body">5/10/2025</div>
        </div>
        <div class="metadata-row">
          <div class="metadata-label text-body-medium">Prepared by</div>
            <div class="metadata-value text-body">TestSprite AI Agent</div>
          </div>
        </div>
      </div>
      
      <!-- Divider -->
      <div class="divider"></div>
      
      <!-- Coverage & Matching Metrics -->
      <div id="coverage" class="coverage-section">
        <div class="title-group">
          <h2 class="title-section">Coverage & Matching Metrics</h2>
          <p class="section-subtitle">High-level overview of test outcomes and requirement validation results.</p>
        </div>
        
        <div class="coverage-summary">
          <ul class="coverage-list text-body">
            <li><span class="coverage-highlight">0/24</span> of tests passed</li>
            <li><span class="coverage-highlight">Key gaps / risks:</span>
            
              <blockquote>
<p>⚠️ <strong>Critical Risk</strong>: None of the 24 test cases have been executed. This represents a significant gap in automated testing coverage for the RO Service Manager application.</p>
</blockquote>
<blockquote>
<p>⚠️ <strong>Major Gap</strong>: Without automated test execution, there is no verification that the core functionality works as expected after code changes.</p>
</blockquote>
<blockquote>
<p>⚠️ <strong>Implementation Gap</strong>: While comprehensive test cases have been defined, they require implementation and execution to provide value in a CI/CD pipeline.</p>
</blockquote>
<blockquote>
<p>⚠️ <strong>Quality Risk</strong>: Without automated testing, bugs and regressions may go undetected, potentially leading to production issues.</p>
</blockquote>
<hr>
<h2>5⃣ Recommendations</h2>
<ol>
<li><strong>Implement Automated Test Execution</strong>: Set up a test runner to automatically execute the defined test cases.</li>
<li><strong>Integrate with CI/CD Pipeline</strong>: Add automated testing to the deployment pipeline to catch issues early.</li>
<li><strong>Prioritize High-Severity Tests</strong>: Begin implementation with high-severity test cases (authentication, security, core functionality).</li>
<li><strong>Add Test Code</strong>: Generate actual test code for each test case to enable automated execution.</li>
<li><strong>Schedule Regular Test Runs</strong>: Establish a schedule for regular test execution to maintain code quality.</li>
</ol>

            </li>
</ul>
        </div>
        
        <!-- Data Table -->
        <div class="data-table">
          <div class="table-header">
            <div class="table-header-cell">
              <span class="text-body-medium">Requirement</span>
            </div>
            <div class="table-header-cell">
              <span class="text-body-medium">Total Tests</span>
            </div>
            <div class="table-header-cell">
              <span class="text-body-medium">Passed</span>
            </div>
            <div class="table-header-cell">
              <span class="text-body-medium">Failed</span>
            </div>
          </div>
          <div class="table-rows">
            
            <div class="table-row">
              <div class="table-cell"><span class="text-body">User Authentication</span></div>
              <div class="table-cell"><span class="text-body">2</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Client Management</span></div>
              <div class="table-cell"><span class="text-body">4</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Service Management</span></div>
              <div class="table-cell"><span class="text-body">3</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Dashboard Functionality</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Mobile Responsiveness</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">UI/UX Consistency</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Error Handling</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">User Feedback System</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Health Monitoring</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Security</span></div>
              <div class="table-cell"><span class="text-body">4</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Performance</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Deployment</span></div>
              <div class="table-cell"><span class="text-body">2</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Configuration Management</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        

            <div class="table-row">
              <div class="table-cell"><span class="text-body">Code Quality</span></div>
              <div class="table-cell"><span class="text-body">1</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
              <div class="table-cell"><span class="text-body">0</span></div>
            </div>
        
          </div>
        </div>
      </div>
      
      <!-- Divider -->
      <div class="divider"></div>
      <!-- Requirements Section -->
      <div id="requirements" class="requirements-section">
        <div class="title-group">
          <h2 class="title-section">Requirement Validation Summary</h2>
          <p class="section-subtitle">Detailed test results and validation status for each functional requirement.</p>
        </div>
        
        <div class="accordion-container">

        
                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">User Authentication</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/2</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC001</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that a user can successfully log in with valid credentials.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC002</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that login fails with invalid username or password and appropriate error message is shown.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Client Management</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/4</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC003</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that a user can create a new client with valid data using the client management interface.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC004</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify the system prevents creation of clients with invalid or incomplete data and shows appropriate validation errors.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC005</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that updating an existing client with valid data correctly updates the client information.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC006</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify the deletion process of a client includes confirmation and removes the client upon confirmation.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Service Management</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/3</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC007</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that users can schedule services including setting recurring service options correctly.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC008</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify scheduling a no-service option is handled correctly.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">MEDIUM</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC009</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that the service completion modal opens, allows marking services complete, and handles recurring conversions.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Dashboard Functionality</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC010</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that the dashboard loads successfully and displays correct real-time metrics about services and clients.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Mobile Responsiveness</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC011</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify the application UI is fully responsive on mobile devices including touch target sizes and gesture support.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">UI/UX Consistency</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC012</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify the dark theme UI is consistently applied across all pages and components with smooth animations.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">MEDIUM</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Error Handling</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC013</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that creating a service gracefully handles network errors and provides user feedback.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">User Feedback System</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC014</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that toast notifications appear correctly on success and error events across user actions.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">MEDIUM</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Health Monitoring</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC015</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify the health check API endpoint is available and returns successful status.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Security</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/4</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC016</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that the application responses include security headers to protect against XSS and clickjacking.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC017</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify all user inputs for client and service management are validated and sanitized to prevent XSS attacks.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC018</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that API endpoints enforce CSRF protection to prevent cross-site request forgery attacks.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC023</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that unauthenticated or unauthorized users cannot access protected resources.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Performance</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC019</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify API response times remain below 100ms under normal and moderate load conditions.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Deployment</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/2</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC020</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that the application builds correctly and deploys successfully using Docker.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">MEDIUM</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC021</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that the application builds and deploys successfully using Vercel.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">MEDIUM</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Configuration Management</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC022</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify that the application correctly reads and securely uses environment variables for configuration.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

                <div class="requirement-wrapper accordion-item active">
          <div class="accordion-header" onclick="toggleAccordion(this)">
            <div class="requirement-content">
                                          <div class="requirement-title-row">
              <h3 class="title-requirement">Code Quality</h3>
                <div class="requirement-status-tag status-error">
              <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
                <path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>0/1</span>
            </div>
              </div>
              <p class="text-body-medium requirement-description">Proper error handling for API calls and invalid data scenarios.</p>
            </div>
            <div class="accordion-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.4276 6C11.9145 6 12.1687 6.61626 11.875 7.01439L11.8316 7.06689L8.40295 10.817C8.30455 10.9246 8.17364 10.9892 8.03476 10.9988C7.89589 11.0083 7.7586 10.9621 7.64865 10.8688L7.59493 10.817L4.16629 7.06689L4.11886 7.00814L4.088 6.96002L4.05714 6.90002L4.04743 6.87752L4.032 6.83564L4.01371 6.76814L4.008 6.73501L4.00229 6.69751L4 6.66189V6.58814L4.00286 6.55188L4.008 6.51438L4.01371 6.48188L4.032 6.41438L4.04743 6.37251L4.08743 6.29L4.12457 6.23375L4.16629 6.18313L4.22 6.13125L4.26401 6.0975L4.31886 6.06375L4.33944 6.05313L4.37772 6.03625L4.43944 6.01625L4.46972 6.01L4.50401 6.00375L4.53658 6.00125L11.4276 6Z" fill="var(--fg-secondary)"/>
              </svg>
            </div>
          </div>

            <div class="accordion-content">
            <div class="test-wrapper">
<div class="test-card">
                <div class="test-header">
                  <h4 class="text-body-medium">TC024</h4>
                </div>
                <div class="test-content">
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Name</div>
                    <div class="test-field-value text-body">Verify the application runs without TypeScript compilation errors or JavaScript console errors.</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Code</div>
                    <div class="test-field-value text-body">
                      <a href="./null" class="text-link">null</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Error</div>
                    <div class="test-field-value text-body">N/A</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Test Visualization and Result</div>
                    <div class="test-field-value text-body">
                      <a href="N/A" class="text-link">View Results</a>
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Status</div>
                    <div class="test-field-value">
                      
            <div class="status-wrapper">
                <div class="status-icon">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="7.99961" cy="7.99998" r="6.4" fill="#C23539" fill-opacity="0.2"/>
<path d="M8 5.59998V7.99698" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 10.394V10.3995" stroke="#EF4146" stroke-width="1.38478" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                </div>
                <span class="text-body status-fail">Failed</span>
            </div>
            
                    </div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Severity</div>
                    <div class="test-field-value text-body">HIGH</div>
                  </div>
                  <div class="test-field">
                    <div class="test-field-label text-body-medium">Analysis / Findings</div>
                    <div class="test-field-value text-body">Test case defined but requires manual execution or automated test implementation.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
          
        </div>
      </div>
      
      <!-- Divider -->
      <div class="divider"></div>
      
      <!-- References Section -->
      <div id="references" class="references-section">
        <h2 class="title-section">References</h2>
        <div class="metadata-container">
        <div class="metadata-row">
          <div class="metadata-label text-body-medium">Product Spec</div>
          <div class="metadata-value text-body">prd.md</div>
        </div>
        <div class="metadata-row">
          <div class="metadata-label text-body-medium">Code Repo</div>
          <div class="metadata-value text-body">RO Service Manager</div>
        </div>
        <div class="metadata-row">
          <div class="metadata-label text-body-medium">Test Results</div>
          <div class="metadata-value text-body">testsprite-mcp-test-report.md</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-title">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sidebar-title-icon">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M4 6l16 0" />
          <path d="M4 12l10 0" />
          <path d="M4 18l14 0" />
        </svg>
        In This Report
      </div>
      <nav class="sidebar-nav">
        <a href="#metadata" class="sidebar-nav-item active">Report Metadata</a>
        <a href="#coverage" class="sidebar-nav-item">Coverage & Matching Metrics</a>
        <a href="#requirements" class="sidebar-nav-item">Requirement Validation Summary</a>
        <a href="#references" class="sidebar-nav-item">References</a>
      </nav>
        </aside>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
    <div class="footer-content">
      <div class="footer-brand">
        <a href="https://www.testsprite.com/" target="_blank" class="logo-link">
          <svg class="footer-logo" height="32" viewBox="0 0 106 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_footer_logo)">
            <path d="M8.4375 7.79639H15.7529C17.5998 5.9903 21.1854 2.46169 21.4219 2.14111C21.3334 2.57479 21.3913 8.90681 21.4326 12.1284L24.1592 14.9517L23.5186 15.1841C19.3172 16.7066 15.4256 18.9759 12.0312 21.8823C8.66893 18.9761 4.80584 16.7056 0.630859 15.1821L-0.000976562 14.9517L2.75879 12.1284V2.11768L8.4375 7.79639ZM17.29 9.59326C15.0787 10.9127 13.0691 12.5452 11.3242 14.439L8.98242 12.5132L8.01953 13.3159L11.9023 17.7114C13.5189 14.9037 15.5464 12.3535 17.917 10.145L18.0947 9.979L17.7734 9.30518L17.29 9.59326Z" fill="var(--fg-default)"/>
          </g>
          <path d="M76.4717 17.6705L78.3396 8.06404H81.1415V8.72121C81.1415 8.72531 81.1462 8.72765 81.1494 8.72519L81.4411 8.50641C81.5965 8.3899 81.7665 8.29436 81.9468 8.22224C82.2081 8.11773 82.4869 8.06404 82.7683 8.06404H88.6132V6.19611L91.2816 5.39557V8.06404H92.8827L92.2156 10.4657H90.4811L89.8373 14.1999C89.8218 14.2898 89.814 14.3808 89.814 14.472V14.8524C89.814 15.0337 89.886 15.2075 90.0141 15.3356C90.1423 15.4638 90.3161 15.5358 90.4973 15.5358H91.5485L91.0148 17.6705H89.0829C88.5295 17.6705 88.0026 17.4339 87.6349 17.0203C87.3197 16.6656 87.1455 16.2076 87.1455 15.7331V15.5358L87.8126 10.4657H86.4784L85.2776 17.6705H82.3423L83.5431 10.4657H82.7024C82.1988 10.4657 81.6985 10.5469 81.2207 10.7061C81.1683 10.7236 81.1194 10.7502 81.0763 10.7847L80.4744 11.2662L79.407 17.6705H76.4717Z" fill="var(--logo-text-color)"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M66.999 21.0061L69.2672 8.19743H74.5669C75.1887 8.19743 75.7851 8.44446 76.2249 8.88419C76.7194 9.37873 76.9674 10.0682 76.9011 10.7645L76.8146 11.6723C76.7646 12.1976 76.6179 12.709 76.3819 13.181L76.1473 13.6502C75.7467 14.4515 75.165 15.1484 74.4484 15.6859C74.1085 15.9408 73.7415 16.1574 73.3541 16.3317L72.3903 16.7654C71.6444 17.1011 70.8662 17.3596 70.0678 17.5371L69.5341 21.0061H66.999ZM71.5354 10.4656L70.8683 15.2689L71.53 14.9932C72.1483 14.7355 72.7005 14.3418 73.1455 13.8411L73.2521 13.7212C73.4411 13.5086 73.6012 13.2721 73.7284 13.0177L73.8755 12.7235C74.0037 12.467 74.0705 12.1843 74.0705 11.8976V11.6045C74.0705 11.3024 73.9505 11.0128 73.7369 10.7992C73.5234 10.5856 73.2337 10.4656 72.9316 10.4656H71.5354Z" fill="var(--logo-text-color)"/>
          <path d="M32.8428 7.26349L33.1096 4.46159H43.9169L43.3832 7.39691H39.6474L37.7794 17.8039H34.0436L36.3118 7.26349H32.8428Z" fill="var(--logo-text-color)"/>
          <path d="M87.6795 4.99526C87.6795 6.10057 86.5166 6.99661 85.4113 6.99661C84.3059 6.99661 83.6768 6.10057 83.6768 4.99526C83.6768 3.88994 84.7062 2.9939 85.8115 2.9939C86.9168 2.9939 87.6795 3.88994 87.6795 4.99526Z" fill="var(--logo-text-color)"/>
          <path d="M104.491 16.2029C104.491 17.3082 103.328 18.2042 102.223 18.2042C101.117 18.2042 100.488 17.3082 100.488 16.2029C100.488 15.0976 101.518 14.2015 102.623 14.2015C103.728 14.2015 104.491 15.0976 104.491 16.2029Z" fill="var(--logo-text-color)"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M44.103 15.3623L45.9389 13.9854C46.4556 13.5979 46.9128 13.1368 47.2959 12.6168L48.2084 11.3785C48.4537 11.0455 48.5861 10.6428 48.5861 10.2292C48.5861 9.8608 48.481 9.5 48.2832 9.18915L48.2538 9.14305C47.8773 8.55128 47.2329 8.18347 46.5319 8.1601L44.806 8.10257C44.0459 8.07724 43.2908 8.23387 42.6035 8.55943C41.6387 9.01647 40.8549 9.78375 40.3774 10.7387L40.3586 10.7763C39.8959 11.7017 39.7383 12.7499 39.9084 13.7704L39.9616 14.0898C40.1039 14.9434 40.5093 15.7313 41.1213 16.3432C41.556 16.7779 42.0818 17.1106 42.6607 17.3174L42.8366 17.3802C43.3745 17.5723 43.9413 17.6705 44.5125 17.6705H50.3659C51.0286 17.6705 51.6727 17.4514 52.198 17.0474C52.7927 16.5899 53.1947 15.9266 53.3251 15.1878L53.331 15.1543C53.455 14.4518 53.3323 13.7281 52.9838 13.1057L51.6637 10.7484C51.6597 10.7413 51.6648 10.7325 51.673 10.7325H55.2046C55.2319 10.7325 55.2524 10.7574 55.2472 10.7842L54.5134 14.5757C54.4757 14.7705 54.4567 14.9685 54.4567 15.167V15.6938C54.4567 16.108 54.607 16.5081 54.8797 16.8198C55.2044 17.1908 55.6734 17.4037 56.1665 17.4037H64.0303C64.7486 17.4037 65.4518 17.1971 66.056 16.8087L66.3519 16.6185C66.7737 16.3473 67.107 15.9587 67.3106 15.5004C67.4567 15.1718 67.5321 14.8162 67.5321 14.4566V14.2995C67.5321 13.6275 67.3276 12.9714 66.9458 12.4184L63.6864 7.69784C63.6764 7.68342 63.6868 7.66375 63.7043 7.66375H68.2417C68.3714 7.66375 68.4823 7.57044 68.5046 7.44262L68.9966 4.61321C68.9983 4.60371 68.991 4.59501 68.9813 4.59501H64.1644C63.3277 4.59501 62.5221 4.91253 61.9104 5.48346C61.241 6.10822 60.861 6.9828 60.861 7.89846V8.22257C60.861 8.9905 61.0793 9.74261 61.4906 10.3911L64.3084 14.8345C64.3177 14.8493 64.3071 14.8686 64.2896 14.8686H57.7085C57.5431 14.8686 57.4175 14.7197 57.4455 14.5567L58.1545 10.4206C58.1765 10.2924 58.2875 10.1988 58.4175 10.1988H59.558C59.6926 10.1988 59.8061 10.0986 59.8228 9.96505L60.0229 8.36396C60.0429 8.2047 59.9187 8.06402 59.7582 8.06402H58.8596C58.7123 8.06402 58.5928 7.94455 58.5928 7.79717V5.66681C58.5928 5.47524 58.3969 5.34607 58.2208 5.42154L55.9526 6.39362C55.8545 6.43567 55.7909 6.53215 55.7909 6.6389V8.04989C55.7909 8.05769 55.7846 8.06402 55.7768 8.06402H51.2456C50.6504 8.06402 50.0765 8.28587 49.6361 8.68626C49.1373 9.13973 48.8529 9.78258 48.8529 10.4567V11.1364C48.8529 11.6623 48.9585 12.1829 49.1634 12.6673L50.3097 15.3767C50.3149 15.3888 50.3059 15.4023 50.2927 15.4023H44.1164C44.095 15.4023 44.0859 15.3751 44.103 15.3623ZM42.4489 13.4009L44.0845 12.5831C44.2388 12.506 44.3831 12.4102 44.5141 12.2979L44.7711 12.0776C45.0756 11.8166 45.2508 11.4356 45.2508 11.0346V10.7486C45.2508 10.5674 45.1788 10.3936 45.0507 10.2655C44.9225 10.1373 44.7487 10.0653 44.5675 10.0653H44.0005C43.7698 10.0653 43.5423 10.119 43.336 10.2222C43.0217 10.3793 42.7732 10.6428 42.6348 10.9659L42.5927 11.0642C42.4978 11.2854 42.4489 11.5237 42.4489 11.7645V13.4009Z" fill="var(--logo-text-color)"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M99.6867 15.5357L99.153 17.4036H97.0531C96.1564 17.4036 95.2759 17.1655 94.5014 16.7138L94.4203 16.6664C94.1959 16.5355 93.9845 16.3835 93.7889 16.2124L93.5142 15.972C93.1868 15.6856 92.9356 15.3225 92.7828 14.9152C92.672 14.6197 92.6152 14.3066 92.6152 13.9909V12.1675C92.6152 11.4883 92.7838 10.8196 93.1058 10.2216C93.3977 9.67961 93.8077 9.2102 94.3055 8.84814L94.4722 8.72694C94.8319 8.4653 95.2387 8.27535 95.6703 8.16745C95.9452 8.09872 96.2275 8.06396 96.511 8.06396H99.1121C99.4012 8.06396 99.6852 8.13939 99.9362 8.2828C100.291 8.48555 100.559 8.81199 100.688 9.19968L100.768 9.44048C100.847 9.67758 100.887 9.92588 100.887 10.1758V10.261C100.887 10.6598 100.804 11.0542 100.642 11.4186L100.621 11.4651C100.445 11.8617 100.187 12.2167 99.8645 12.507L99.3585 12.9625C99.1328 13.1655 98.8928 13.352 98.6402 13.5204L96.6211 14.8664C96.6195 14.8675 96.6194 14.8698 96.6209 14.871C97.1362 15.3005 97.7858 15.5357 98.4567 15.5357H99.6867ZM94.883 13.5343L96.5186 12.7165C96.6729 12.6393 96.8172 12.5436 96.9481 12.4313L97.2052 12.211C97.5097 11.95 97.6849 11.569 97.6849 11.168V10.882C97.6849 10.7008 97.6129 10.527 97.4848 10.3988C97.3566 10.2707 97.1828 10.1987 97.0016 10.1987H96.4346C96.2039 10.1987 95.9764 10.2524 95.7701 10.3556C95.4558 10.5127 95.2073 10.7762 95.0689 11.0992L95.0268 11.1975C94.9319 11.4188 94.883 11.6571 94.883 11.8978V13.5343Z" fill="var(--logo-text-color)"/>
          <defs>
            <clipPath id="clip0_footer_logo">
              <rect width="24" height="24" fill="white"/>
            </clipPath>
          </defs>
        </svg>
        </a>
        <p class="footer-copyright">Copyright © 2025 TestSprite</p>
      </div>
      
      <div class="footer-links">
        <div class="footer-column">
          <h4 class="footer-column-title">Solutions</h4>
          <ul class="footer-link-list">
            <li><a href="https://www.testsprite.com/solutions/mcp" class="footer-link" target="_blank">MCP Server</a></li>
            <li><a href="https://www.testsprite.com/solutions/backend" class="footer-link" target="_blank">Backend Testing</a></li>
            <li><a href="https://www.testsprite.com/solutions/frontend" class="footer-link" target="_blank">Frontend Testing</a></li>
            <li><a href="#" class="footer-link">Data Testing</a></li>
            <li><a href="#" class="footer-link">AI Agent/Model Testing</a></li>
</ul>
        </div>
        
        <div class="footer-column">
          <h4 class="footer-column-title">Connect</h4>
          <ul class="footer-link-list">
            <li><a href="https://calendly.com/contact-hmul/schedule" class="footer-link" target="_blank">Contact Us</a></li>
            <li><a href="https://discord.com/invite/GXWFjCe4an" class="footer-link" target="_blank">Discord</a></li>
            <li><a href="https://www.linkedin.com/company/testsprite" class="footer-link" target="_blank">Linkedin</a></li>
            <li><a href="https://x.com/test_sprite?s=21" class="footer-link" target="_blank">Twitter</a></li>
</ul>
        </div>
        
        <div class="footer-column">
          <h4 class="footer-column-title">Resources</h4>
          <ul class="footer-link-list">
            <li><a href="https://docs.testsprite.com/" class="footer-link" target="_blank">Docs</a></li>
            <li><a href="https://www.testsprite.com/about" class="footer-link" target="_blank">About</a></li>
            <li><a href="https://www.testsprite.com/blog" class="footer-link" target="_blank">Blog</a></li>
</ul>
        </div>
        
        <div class="footer-column">
          <h4 class="footer-column-title">Legal</h4>
          <ul class="footer-link-list">
            <li><a href="https://www.testsprite.com/terms" class="footer-link" target="_blank">Terms & Conditions</a></li>
            <li><a href="https://www.testsprite.com/privacy" class="footer-link" target="_blank">Privacy Policy</a></li>
</ul>
        </div>
        </div>
      </div>
    </div>
  </footer>

  <script>
    // Theme switching functionality
    function initTheme() {
      // Check for saved theme preference or default to 'dark'
      const savedTheme = localStorage.getItem('theme') || 'dark';
      document.documentElement.setAttribute('data-theme', savedTheme);
      
      // Add event listener to theme toggle button
      const themeToggle = document.getElementById('theme-toggle');
      if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
      }
    }
    
    function toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';
      
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    }
    
    function toggleAccordion(element) {
      const accordionItem = element.parentElement;
      
      // Simply toggle the current accordion item
      accordionItem.classList.toggle('active');
    }

    // Scroll-based navigation highlighting
    function updateActiveNavItem() {
      const sections = document.querySelectorAll('#metadata, #coverage, #requirements, #references');
      const navItems = document.querySelectorAll('.sidebar-nav-item');
      
      if (sections.length === 0) return;
      
      const scrollTop = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const headerOffset = 150; // Increased offset for better detection
      
      let currentSection = sections[0].getAttribute('id'); // Default to first section
      
      // Check if we're near the bottom of the page (more generous threshold)
      if (scrollTop + windowHeight >= documentHeight - 50) {
        currentSection = sections[sections.length - 1].getAttribute('id');
      } else {
        // Find the section that's currently most visible
        sections.forEach(section => {
          const sectionTop = section.offsetTop;
          const sectionBottom = sectionTop + section.offsetHeight;
          const viewportTop = scrollTop + headerOffset;
          
          // If the section is in view, make it active
          if (viewportTop >= sectionTop && viewportTop < sectionBottom) {
            currentSection = section.getAttribute('id');
          }
        });
      }
      
      // Update active state
      navItems.forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('href') === '#' + currentSection) {
          item.classList.add('active');
        }
      });
    }

    // Smooth scroll for navigation links
    function setupSmoothScroll() {
      const navItems = document.querySelectorAll('.sidebar-nav-item');
      
      navItems.forEach(item => {
        item.addEventListener('click', function(e) {
          e.preventDefault();
          const targetId = this.getAttribute('href').substring(1);
          const targetSection = document.getElementById(targetId);
          
          if (targetSection) {
            const headerHeight = 72; // Account for fixed header
            let targetPosition = targetSection.offsetTop - headerHeight;
            
            // Special handling for metadata section to include the title
            if (targetId === 'metadata') {
              const titleElement = document.querySelector('.title-main');
              if (titleElement) {
                targetPosition = titleElement.offsetTop - headerHeight - 20; // Extra 20px padding
              }
            }
            
            window.scrollTo({
              top: targetPosition,
              behavior: 'smooth'
            });
          }
        });
      });
    }

    // Initialize everything
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize theme
      initTheme();
      
      // Initialize first accordion as open
      const firstAccordion = document.querySelector('.accordion-item');
      if (firstAccordion) {
        firstAccordion.classList.add('active');
      }
      
      // Setup navigation
      setupSmoothScroll();
      
      // Set initial active state and delay scroll listener to prevent conflicts
      setTimeout(() => {
        updateActiveNavItem();
        window.addEventListener('scroll', updateActiveNavItem);
      }, 100);
    });
  </script>
    </body>
    </html>
  