{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/api/:path*", "destination": "/api/:path*", "regex": "^\\/api(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}], "regex": "^\\/sw\\.js(?:\\/)?$"}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^\\/manifest\\.json(?:\\/)?$"}]}