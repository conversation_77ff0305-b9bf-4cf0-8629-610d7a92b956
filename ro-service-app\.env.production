# Production Environment Variables
# Copy this file to .env.local and update with your production values

# Database Configuration (Required)
MONGODB_URI=mongodb+srv://kavan2673_db_user:Ka<PERSON>%<EMAIL>/ro-service-app?retryWrites=true&w=majority

# Authentication Configuration (Required)
NEXTAUTH_SECRET=your-super-secret-nextauth-key-minimum-32-characters-long
NEXTAUTH_URL=https://your-domain.com

# Application Configuration
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Optional: Custom API URL (if using external API)
# NEXT_PUBLIC_API_URL=https://your-api-domain.com/api
