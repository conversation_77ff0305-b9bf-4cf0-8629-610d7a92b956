import mongoose from 'mongoose';

const ClientSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Client name is required'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    validate: {
      validator: function(v) {
        return /^\+91[6-9]\d{9}$/.test(v);
      },
      message: 'Please enter a valid Indian phone number with +91 prefix'
    }
  },
  location: {
    type: String,
    required: [true, 'Service location is required'],
    trim: true,
    maxlength: [500, 'Location cannot be more than 500 characters']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot be more than 1000 characters']
  },
  serviceType: {
    type: String,
    enum: ['recurring', 'scheduled', 'none'],
    default: 'recurring',
    required: [true, 'Service type is required']
  },
  serviceInterval: {
    type: Number,
    enum: [1, 2, 3, 4, 5, 6, 12],
    default: 3,
    required: function() {
      return this.serviceType === 'recurring';
    }
  },
  scheduledDate: {
    type: Date,
    required: function() {
      return this.serviceType === 'scheduled';
    }
  },
  lastServiceDate: {
    type: Date,
    default: null
  },
  nextServiceDate: {
    type: Date,
    required: function() {
      return this.serviceType !== 'none';
    },
    default: function() {
      if (this.serviceType === 'none') {
        return new Date('2099-12-31');
      }
      const baseDate = this.lastServiceDate || new Date();
      const nextDate = new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate());
      nextDate.setMonth(nextDate.getMonth() + (this.serviceInterval || 3));
      return nextDate;
    }
  }
}, {
  timestamps: true
});

// Calculate next service date before validation
ClientSchema.pre('validate', function(next) {
  console.log('Client pre-validate hook - calculating nextServiceDate for:', {
    id: this._id,
    serviceType: this.serviceType,
    serviceInterval: this.serviceInterval,
    scheduledDate: this.scheduledDate,
    lastServiceDate: this.lastServiceDate,
    isModified: this.isModified('nextServiceDate'),
    isNew: this.isNew
  });

  // Recalculate nextServiceDate based on service type
  if (this.serviceType === 'scheduled') {
    // For scheduled services, set nextServiceDate to the scheduled date
    if (this.scheduledDate) {
      const schedDate = new Date(this.scheduledDate);
      this.nextServiceDate = new Date(Date.UTC(schedDate.getUTCFullYear(), schedDate.getUTCMonth(), schedDate.getUTCDate()));
      console.log('Calculated nextServiceDate for scheduled service:', this.nextServiceDate);
    }
  } else if (this.serviceType === 'none') {
    // For clients with no service scheduled, set nextServiceDate to far future
    this.nextServiceDate = new Date('2099-12-31');
    console.log('Set nextServiceDate to far future for client with no service');
  } else if (this.serviceType === 'recurring') {
    // For recurring services, recalculate based on service interval
    if (!this.isModified('nextServiceDate') || this.isNew || this.isModified('serviceInterval')) {
      if (this.lastServiceDate) {
        const baseDate = new Date(this.lastServiceDate);
        const nextDate = new Date(Date.UTC(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()));
        const interval = this.serviceInterval || 3;
        nextDate.setUTCMonth(nextDate.getUTCMonth() + interval);
        this.nextServiceDate = nextDate;
        console.log('Calculated nextServiceDate for recurring service based on lastServiceDate:', {
          baseDate,
          interval,
          nextServiceDate: this.nextServiceDate
        });
      } else {
        // For recurring services without lastServiceDate, use current date + interval
        const baseDate = new Date();
        const nextDate = new Date(Date.UTC(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()));
        const interval = this.serviceInterval || 3;
        nextDate.setUTCMonth(nextDate.getUTCMonth() + interval);
        this.nextServiceDate = nextDate;
        console.log('Calculated nextServiceDate for recurring service:', {
          baseDate,
          interval,
          nextServiceDate: this.nextServiceDate
        });
      }
    } else {
      console.log('nextServiceDate was explicitly modified, keeping the set value:', this.nextServiceDate);
    }
  }
  next();
});

// Optimized indexes for efficient queries
// Primary index for dashboard queries (most frequent)
ClientSchema.index({ nextServiceDate: 1, serviceType: 1 });

// Index for search functionality
ClientSchema.index({ name: 'text', location: 'text', phone: 'text' });

// Unique index for phone numbers to prevent duplicates
ClientSchema.index({ phone: 1 }, { unique: true });

// Compound index for service type filtering
ClientSchema.index({ serviceType: 1, nextServiceDate: 1 });

// Index for date range queries (overdue services)
ClientSchema.index({ nextServiceDate: 1, updatedAt: -1 });

// Sparse index for scheduled services
ClientSchema.index({ scheduledDate: 1 }, { sparse: true });

// Index for recurring services with interval
ClientSchema.index({ serviceType: 1, serviceInterval: 1 }, {
  partialFilterExpression: { serviceType: 'recurring' }
});

export default mongoose.models.Client || mongoose.model('Client', ClientSchema);