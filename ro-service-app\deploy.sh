#!/bin/bash

# RO Service Manager Deployment Script

echo "🚀 Starting RO Service Manager deployment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo "⚠️  .env.local not found. Creating from template..."
    cp .env.example .env.local
    echo "📝 Please edit .env.local with your configuration before running the app."
fi

# Build the application
echo "🔨 Building the application..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo ""
    echo "🎉 Deployment complete!"
    echo ""
    echo "To start the application:"
    echo "  npm start"
    echo ""
    echo "To start in development mode:"
    echo "  npm run dev"
    echo ""
    echo "Make sure to:"
    echo "1. Configure your .env.local file"
    echo "2. Ensure MongoDB is running"
    echo "3. Set up your production environment variables"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi