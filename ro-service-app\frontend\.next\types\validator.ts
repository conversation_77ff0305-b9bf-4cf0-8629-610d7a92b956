// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../app/clients/[id]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/clients/[id]">> = Specific
  const handler = {} as typeof import("../../app/clients/[id]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/clients/new/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/clients/new">> = Specific
  const handler = {} as typeof import("../../app/clients/new/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/clients/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/clients">> = Specific
  const handler = {} as typeof import("../../app/clients/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/login/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/login">> = Specific
  const handler = {} as typeof import("../../app/login/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/auth/login/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/auth/login">> = Specific
  const handler = {} as typeof import("../../app/api/auth/login/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/auth/logout/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/auth/logout">> = Specific
  const handler = {} as typeof import("../../app/api/auth/logout/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/clients/[id]/convert-to-recurring/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/clients/[id]/convert-to-recurring">> = Specific
  const handler = {} as typeof import("../../app/api/clients/[id]/convert-to-recurring/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/clients/[id]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/clients/[id]">> = Specific
  const handler = {} as typeof import("../../app/api/clients/[id]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/clients/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/clients">> = Specific
  const handler = {} as typeof import("../../app/api/clients/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/dashboard/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/dashboard">> = Specific
  const handler = {} as typeof import("../../app/api/dashboard/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/services/[id]/complete/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/services/[id]/complete">> = Specific
  const handler = {} as typeof import("../../app/api/services/[id]/complete/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/api/services/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/services">> = Specific
  const handler = {} as typeof import("../../app/api/services/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../app/clients/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/clients">> = Specific
  const handler = {} as typeof import("../../app/clients/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
