@echo off
REM RO Service Manager Deployment Script for Windows

echo 🚀 Starting RO Service Manager deployment...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    exit /b 1
)

REM Install dependencies
echo 📦 Installing dependencies...
npm ci

REM Check if .env.local exists
if not exist .env.local (
    echo ⚠️  .env.local not found. Creating from template...
    copy .env.example .env.local
    echo 📝 Please edit .env.local with your configuration before running the app.
)

REM Build the application
echo 🔨 Building the application...
npm run build

if %errorlevel% equ 0 (
    echo ✅ Build successful!
    echo.
    echo 🎉 Deployment complete!
    echo.
    echo To start the application:
    echo   npm start
    echo.
    echo To start in development mode:
    echo   npm run dev
    echo.
    echo Make sure to:
    echo 1. Configure your .env.local file
    echo 2. Ensure MongoDB is running
    echo 3. Set up your production environment variables
) else (
    echo ❌ Build failed. Please check the errors above.
    exit /b 1
)