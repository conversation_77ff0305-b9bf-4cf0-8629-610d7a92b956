/** @type {import('next').NextConfig} */
const nextConfig = {
  // Build Configuration - Temporarily disable ESLint for testing
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: false,
  },

  // Output configuration for deployment
  output: 'standalone',
  
  // Performance Optimizations
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },
  
  // Image Configuration
  images: {
    domains: [], // Add your image domains here
    unoptimized: process.env.NODE_ENV === 'development',
  },
  
  // Development Configuration
  devIndicators: {
    position: 'bottom-right',
  },
  
  // Production Optimizations
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  
  // Webpack Configuration
  webpack: (config: any, { dev, isServer }: { dev: boolean; isServer: boolean }) => {
    // Disable source maps in development for faster builds
    if (dev && !isServer) {
      config.devtool = false;
    }
    return config;
  },
  
  // Security Headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
        ],
      },
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  
  // Redirects (if needed)
  async redirects() {
    return [];
  },
  
  // Rewrites (if needed)
  async rewrites() {
    return [
      // Proxy API requests to the backend API routes
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ];
  },
  
  // Development configuration for cross-origin requests
  // This needs to be properly configured for Next.js 15
  // For now, we'll remove it as it's causing warnings
};

module.exports = nextConfig;