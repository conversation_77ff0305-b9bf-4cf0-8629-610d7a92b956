import mongoose from 'mongoose';

// Use NEXT_PUBLIC_ prefix for frontend environment variables or use a proxy approach
const MONGODB_URI = process.env.NEXT_PUBLIC_MONGODB_URI || process.env.MONGODB_URI;

// Only throw error in server-side contexts
if (typeof window === 'undefined' && !MONGODB_URI) {
  // Check if we're in a Next.js API route or server component
  if (process.env.NEXT_RUNTIME === 'nodejs' || process.env.NEXT_RUNTIME === 'edge') {
    console.warn('MONGODB_URI not found in environment variables - using fallback');
  }
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections from growing exponentially
 * during API Route usage.
 */
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function dbConnect() {
  // Skip database connection in browser
  if (typeof window !== 'undefined') {
    console.warn('Skipping database connection in browser environment');
    return null;
  }

  if (cached.conn) {
    return cached.conn;
  }

  // If no MONGODB_URI, return null instead of throwing error
  if (!MONGODB_URI) {
    console.warn('No MONGODB_URI available - database connection skipped');
    return null;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    };

    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose;
    }).catch((error) => {
      console.error('Database connection failed:', error);
      return null;
    });
  }
  
  try {
    cached.conn = await cached.promise;
    return cached.conn;
  } catch (error) {
    console.error('Database connection error:', error);
    return null;
  }
}

/**
 * Check database health and return detailed information
 */
async function checkDatabaseHealth() {
  try {
    const connection = await dbConnect();
    if (!connection) {
      return {
        status: 'unhealthy',
        connected: false,
        error: 'No database connection available'
      };
    }
    
    const db = connection.connection.db;
    
    // Get database stats
    const stats = await db.admin().serverStatus();
    
    return {
      status: 'healthy',
      connected: true,
      host: stats.host,
      port: stats.port,
      name: db.databaseName,
      version: stats.version,
      uptime: stats.uptime
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      connected: false,
      error: error.message
    };
  }
}

export default dbConnect;
export { checkDatabaseHealth };