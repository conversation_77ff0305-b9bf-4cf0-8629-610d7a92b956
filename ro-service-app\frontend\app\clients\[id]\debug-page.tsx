'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function DebugClientPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [clientId, setClientId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const resolveParams = async () => {
      try {
        const resolvedParams = await params;
        setClientId(resolvedParams.id);
        setLoading(false);
      } catch (err) {
        setError('Failed to resolve client ID');
        setLoading(false);
      }
    };

    resolveParams();
  }, [params]);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-slate-100">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mx-auto mb-4"></div>
          <p>Loading client information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">Error</div>
          <p className="text-slate-300 mb-6">{error}</p>
          <Link 
            href="/clients" 
            className="btn-primary"
          >
            Back to Clients
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 px-4 sm:px-6 lg:px-8 py-6">
      <div className="max-w-2xl mx-auto">
        <div className="futuristic-card p-6">
          <h1 className="text-2xl font-bold text-slate-100 mb-6">Debug Client Page</h1>
          
          <div className="space-y-4">
            <div className="bg-slate-800 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-slate-200 mb-2">Client ID Information</h2>
              <p className="text-slate-300">
                <span className="font-medium">Client ID:</span> {clientId}
              </p>
            </div>
            
            <div className="bg-slate-800 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-slate-200 mb-2">Routing Test</h2>
              <p className="text-slate-300 mb-4">
                If you can see this page, the dynamic routing is working correctly.
              </p>
              <button
                onClick={() => router.push(`/clients/${clientId}`)}
                className="btn-primary mr-3"
              >
                Go to Edit Page
              </button>
              <Link 
                href="/clients" 
                className="btn-secondary"
              >
                Back to Clients
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}