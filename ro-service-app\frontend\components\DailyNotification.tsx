'use client';

import { useEffect } from 'react';
import NotificationDisplay from './NotificationDisplay';

const DailyNotification = () => {
  // This component will handle daily notifications
  // For now, it just renders the notification display
  
  useEffect(() => {
    // You can add daily notification logic here
    // For example, checking for overdue services on app load
  }, []);

  return <NotificationDisplay />;
};

export default DailyNotification;