const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env.local file
dotenv.config({ path: path.join(__dirname, '.env.local') });

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev, dir: path.join(__dirname, 'frontend') });
const handle = app.getRequestHandler();

// Use the PORT environment variable if available (for Render), otherwise use the port scanning approach
const PORT = process.env.PORT || null;

// List of ports to try in order (only used if PORT is not set)
const ports = [3000, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009,3010,3011,3012,3013,3014,3015,3016,3017,3018,3019,3020,3021,3022,3023,3024,3025,3026,3027,3028,3029,3030,3031,3032,3033,3034,3035,3036,3037,3038,3039,3040,3041,3042];

async function startServer() {
  await app.prepare();
  
  // If PORT environment variable is set, use it directly
  if (PORT) {
    try {
      const server = createServer((req, res) => {
        const parsedUrl = parse(req.url, true);
        // Let Next.js handle all requests, including API routes
        handle(req, res, parsedUrl);
      });
      
      server.listen(PORT, (err) => {
        if (err) {
          console.error('Error starting server:', err);
          process.exit(1);
        }
        console.log(`> Ready on http://localhost:${PORT}`);
      });
      
      return server;
    } catch (err) {
      console.error('Error starting server:', err);
      process.exit(1);
    }
  } else {
    // Try each port in sequence
    for (const port of ports) {
      try {
        const server = createServer((req, res) => {
          const parsedUrl = parse(req.url, true);
          // Let Next.js handle all requests, including API routes
          handle(req, res, parsedUrl);
        });
        
        // Attempt to listen on the port
        await new Promise((resolve, reject) => {
          const listener = server.listen(port, (err) => {
            if (err) {
              reject(err);
            } else {
              resolve(listener);
            }
          });
          
          // If the port is already in use, close the server and try the next port
          server.on('error', (err) => {
            if (err.code === 'EADDRINUSE') {
              console.log(`Port ${port} is already in use, trying next port...`);
              listener.close();
              reject(err);
            } else {
              reject(err);
            }
          });
        });
        
        console.log(`> Ready on http://localhost:${port}`);
        return server;
      } catch (err) {
        if (err.code !== 'EADDRINUSE') {
          console.error('Error starting server:', err);
          process.exit(1);
        }
        // If EADDRINUSE, continue to the next port
      }
    }
    
    console.error('Unable to find an available port. Tried ports:', ports.join(', '));
    process.exit(1);
  }
}

startServer().catch((err) => {
  console.error('Failed to start server:', err);
  process.exit(1);
});