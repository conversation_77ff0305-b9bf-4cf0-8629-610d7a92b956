'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function TestPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  
  return (
    <div className="min-h-screen bg-slate-900 px-4 py-6">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-slate-100 mb-4">Test Page</h1>
        <p className="text-slate-300 mb-4">Client ID: {params.id}</p>
        <Link 
          href="/clients" 
          className="text-indigo-400 hover:text-indigo-300"
        >
          Back to Clients
        </Link>
      </div>
    </div>
  );
}