// Secure authentication utilities
import jwt from 'jsonwebtoken';
import { getEnv } from '@/lib/env-validation';

export interface User {
  id: string;
  username: string;
  name: string;
  role: 'admin' | 'user';
  email?: string;
}

export interface TokenPayload {
  userId: string;
  username: string;
  role: string;
  name: string;
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

// Token validation with proper error handling
export const validateToken = (token: string): TokenPayload | null => {
  try {
    const env = getEnv();
    const secret = env.JWT_SECRET || env.NEXTAUTH_SECRET;
    
    // Check if secret is available
    if (!secret) {
      console.error('No secret available for token validation');
      return null;
    }
    
    const decoded = jwt.verify(token, secret, {
      issuer: 'ro-service-app',
      audience: 'ro-service-users',
      algorithms: ['HS256']
    }) as TokenPayload;
    
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      console.warn('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      console.warn('Invalid token');
    } else {
      console.error('Token validation error:', error);
    }
    return null;
  }
};

// Password validation with security requirements
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (password.length > 128) {
    errors.push('Password cannot exceed 128 characters');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  // Check for common weak passwords
  const commonPasswords = ['password', '12345678', 'qwerty', 'abc123', 'password123'];
  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('Password is too common and easily guessable');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Secure localStorage operations with validation
export const getStoredUser = (): User | null => {
  if (typeof window === 'undefined') return null;

  try {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (!token || !userData) return null;

    // Validate token
    const validatedToken = validateToken(token);
    if (!validatedToken) {
      clearUserData();
      return null;
    }

    const user = JSON.parse(userData) as User;

    // Validate user data structure
    if (!user.id || !user.username || !user.role) {
      clearUserData();
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error getting stored user:', error);
    clearUserData();
    return null;
  }
};

// Get stored authentication token
export const getStoredToken = (): string | null => {
  if (typeof window === 'undefined') return null;

  try {
    const token = localStorage.getItem('auth_token');
    if (!token) return null;

    // Validate token before returning
    const validatedToken = validateToken(token);
    if (!validatedToken) {
      clearUserData();
      return null;
    }

    return token;
  } catch (error) {
    console.error('Error getting stored token:', error);
    clearUserData();
    return null;
  }
};

// Get authorization header for API requests
export const getAuthHeader = (): { Authorization: string } | {} => {
  const token = getStoredToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Authenticated fetch wrapper
export const authenticatedFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const authHeaders = getAuthHeader();

  const config: RequestInit = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
      ...options.headers,
    },
  };

  const response = await fetch(url, config);

  // Handle 401 responses by clearing auth data and redirecting to login
  if (response.status === 401) {
    console.warn('Authentication failed, clearing session and redirecting to login');
    clearUserData();
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  }

  return response;
};

// Secure data clearing
export const clearUserData = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    
    // Clear any other auth-related data
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('session_id');
  } catch (error) {
    console.error('Error clearing user data:', error);
  }
};

// Authentication status check
export const isAuthenticated = (): boolean => {
  return getStoredUser() !== null;
};

// Generate secure random string for secrets
export const generateSecureSecret = (length: number = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
};

// Session management
export const createSession = (user: User, token: string): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('auth_token', token);
    localStorage.setItem('user_data', JSON.stringify(user));
    localStorage.setItem('session_created', new Date().toISOString());
  } catch (error) {
    console.error('Error creating session:', error);
    throw new Error('Failed to create session');
  }
};

// Check session validity
export const isSessionValid = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    const token = localStorage.getItem('auth_token');
    const sessionCreated = localStorage.getItem('session_created');
    
    if (!token || !sessionCreated) return false;
    
    // Check if session is older than 24 hours
    const created = new Date(sessionCreated);
    const now = new Date();
    const hoursDiff = (now.getTime() - created.getTime()) / (1000 * 60 * 60);
    
    if (hoursDiff > 24) {
      clearUserData();
      return false;
    }
    
    return validateToken(token) !== null;
  } catch (error) {
    console.error('Error checking session validity:', error);
    clearUserData();
    return false;
  }
};

// Client-side login function
export const clientLogin = async (username: string, password: string): Promise<{ success: boolean; user?: User; token?: string; error?: string }> => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    const data = await response.json();

    if (response.ok && data.success) {
      return {
        success: true,
        user: data.user,
        token: data.token
      };
    } else {
      return {
        success: false,
        error: data.error || 'Login failed'
      };
    }
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: 'Network error occurred'
    };
  }
};

// Store user data (alias for createSession)
export const storeUserData = (user: User, token: string): void => {
  createSession(user, token);
};

// Logout function
export const logout = (): void => {
  clearUserData();
  if (typeof window !== 'undefined') {
    window.location.href = '/login';
  }
};

// Token generation with proper security
export const generateToken = (user: User): string => {
  const env = getEnv();
  const secret = env.JWT_SECRET || env.NEXTAUTH_SECRET;
  
  // Check if secret is available
  if (!secret) {
    throw new Error('No secret available for token generation');
  }
  
  const payload: Omit<TokenPayload, 'iat' | 'exp' | 'iss' | 'aud'> = {
    userId: user.id,
    username: user.username,
    role: user.role,
    name: user.name
  };
  
  return jwt.sign(payload, secret, {
    expiresIn: '24h',
    issuer: 'ro-service-app',
    audience: 'ro-service-users',
    algorithm: 'HS256'
  });
};

// Rate limiting for login attempts (in-memory, resets on page reload)
const loginAttempts = new Map<string, { count: number; lastAttempt: number; blocked: boolean }>();

export const checkRateLimit = (identifier: string): { allowed: boolean; remainingAttempts: number; blockTimeRemaining: number } => {
  const now = Date.now();
  const maxAttempts = 5;
  const blockDuration = 15 * 60 * 1000; // 15 minutes
  const windowDuration = 15 * 60 * 1000; // 15 minutes
  
  const attempts = loginAttempts.get(identifier);
  
  if (!attempts) {
    loginAttempts.set(identifier, { count: 1, lastAttempt: now, blocked: false });
    return { allowed: true, remainingAttempts: maxAttempts - 1, blockTimeRemaining: 0 };
  }
  
  // Check if block period has expired
  if (attempts.blocked && (now - attempts.lastAttempt) > blockDuration) {
    loginAttempts.set(identifier, { count: 1, lastAttempt: now, blocked: false });
    return { allowed: true, remainingAttempts: maxAttempts - 1, blockTimeRemaining: 0 };
  }
  
  // If currently blocked
  if (attempts.blocked) {
    const timeRemaining = blockDuration - (now - attempts.lastAttempt);
    return { allowed: false, remainingAttempts: 0, blockTimeRemaining: Math.ceil(timeRemaining / 1000) };
  }
  
  // Reset counter if window has expired
  if ((now - attempts.lastAttempt) > windowDuration) {
    loginAttempts.set(identifier, { count: 1, lastAttempt: now, blocked: false });
    return { allowed: true, remainingAttempts: maxAttempts - 1, blockTimeRemaining: 0 };
  }
  
  // Check if max attempts reached
  if (attempts.count >= maxAttempts) {
    attempts.blocked = true;
    attempts.lastAttempt = now;
    return { allowed: false, remainingAttempts: 0, blockTimeRemaining: Math.ceil(blockDuration / 1000) };
  }
  
  // Increment attempt count
  attempts.count++;
  attempts.lastAttempt = now;
  
  return { 
    allowed: true, 
    remainingAttempts: maxAttempts - attempts.count, 
    blockTimeRemaining: 0 
  };
};

// Input sanitization
export const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential XSS characters
    .substring(0, 1000); // Limit length
};