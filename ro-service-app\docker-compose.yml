version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - MONGODB_URI=mongodb://mongo:27017/ro-service-app
      - NEXTAUTH_SECRET=your-secure-secret-key-here
      - NEXTAUTH_URL=http://localhost:3000
      - NODE_ENV=production
    depends_on:
      - mongo
    networks:
      - ro-network

  mongo:
    image: mongo:7
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    networks:
      - ro-network

volumes:
  mongo_data:

networks:
  ro-network:
    driver: bridge