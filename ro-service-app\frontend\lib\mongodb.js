import mongoose from 'mongoose';

// Use NEXT_PUBLIC_ prefix for frontend environment variables or use a proxy approach
const MONGODB_URI = process.env.NEXT_PUBLIC_MONGODB_URI || process.env.MONGODB_URI;

console.log('=== MONGODB CONNECTION INIT ===');
console.log('MONGODB_URI from env:', MONGODB_URI ? 'Exists' : 'Missing');
console.log('Window object exists:', typeof window !== 'undefined');
console.log('Process env keys:', Object.keys(process.env).filter(key => key.includes('MONGO')));

// Only throw error in server-side contexts
if (typeof window === 'undefined' && !MONGODB_URI) {
  // Check if we're in a Next.js API route or server component
  if (process.env.NEXT_RUNTIME === 'nodejs' || process.env.NEXT_RUNTIME === 'edge') {
    console.warn('MONGODB_URI not found in environment variables - using fallback');
  }
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections from growing exponentially
 * during API Route usage.
 */
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function dbConnect() {
  // Skip database connection in browser
  if (typeof window !== 'undefined') {
    console.warn('Skipping database connection in browser environment');
    return null;
  }

  // Log environment variables for debugging (without sensitive data)
  console.log('=== DATABASE CONNECTION ATTEMPT ===');
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('MONGODB_URI exists:', !!MONGODB_URI);
  console.log('MONGODB_URI length:', MONGODB_URI ? MONGODB_URI.length : 0);

  if (!MONGODB_URI) {
    console.warn('No MONGODB_URI available - database connection skipped');
    return null;
  }

  if (cached.conn) {
    console.log('Using cached database connection');
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: true,
      // Connection pool settings for better performance
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      // Heartbeat settings for connection monitoring
      heartbeatFrequencyMS: 10000,
      // Retry settings
      retryWrites: true,
      retryReads: true,
    };

    console.log('Creating new database connection...');
    console.log('Mongoose version:', mongoose.version);
    console.log('Mongoose readyState before connect:', mongoose.connection.readyState);
    
    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      console.log('Database connection established successfully');
      console.log('Mongoose readyState after connect:', mongoose.connection.readyState);
      return mongoose;
    }).catch((error) => {
      console.error('=== DATABASE CONNECTION FAILED ===');
      console.error('Error message:', error.message);
      console.error('Error code:', error.code);
      console.error('Error name:', error.name);
      console.error('Full error:', error);
      console.error('=== END DATABASE ERROR ===');
      return null;
    });
  }
  
  try {
    console.log('Awaiting database connection promise...');
    cached.conn = await cached.promise;
    console.log('Database connection promise resolved:', !!cached.conn);
    return cached.conn;
  } catch (error) {
    console.error('Database connection error:', error);
    return null;
  }
}

/**
 * Check database health and return detailed information
 */
async function checkDatabaseHealth() {
  try {
    const connection = await dbConnect();
    if (!connection) {
      return {
        status: 'unhealthy',
        connected: false,
        error: 'No database connection available'
      };
    }
    
    const db = connection.connection.db;
    
    // Get database stats
    const stats = await db.admin().serverStatus();
    
    return {
      status: 'healthy',
      connected: true,
      host: stats.host,
      port: stats.port,
      name: db.databaseName,
      version: stats.version,
      uptime: stats.uptime
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      connected: false,
      error: error.message
    };
  }
}

export default dbConnect;
export { checkDatabaseHealth };