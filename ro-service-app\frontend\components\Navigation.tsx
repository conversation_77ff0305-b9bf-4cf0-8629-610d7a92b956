'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Users, Plus, LogOut, User } from 'lucide-react';
import { cn } from '@/utils/index';
import { useAuth } from '@/components/AuthContext';

const Navigation = () => {
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const navItems = [
    {
      href: '/',
      label: 'Dashboard',
      icon: Home,
    },
    {
      href: '/clients',
      label: 'Clients',
      icon: Users,
    },
    {
      href: '/clients/new',
      label: 'Add Client',
      icon: Plus,
    },
  ];

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:block bg-slate-800 border-b border-slate-700 shadow-lg overflow-x-hidden">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16 min-w-0">
            <div className="flex items-center min-w-0 flex-shrink-0">
              <Link href="/" className="flex items-center space-x-2 min-w-0">
                <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-cyan-500 rounded-lg flex items-center justify-center glow flex-shrink-0">
                  <span className="text-white font-bold text-lg">RO</span>
                </div>
                <span className="text-xl font-bold gradient-text whitespace-nowrap">
                  Service Manager
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4 lg:space-x-8 min-w-0">
              <div className="flex space-x-4 lg:space-x-8">
                {navItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-300',
                        isActive
                          ? 'border-indigo-400 text-indigo-400 glow'
                          : 'border-transparent text-slate-300 hover:text-slate-100 hover:border-indigo-400/50'
                      )}
                    >
                      <Icon className="w-4 h-4 mr-2" />
                      {item.label}
                    </Link>
                  );
                })}
              </div>
              
              {/* User Menu */}
              <div className="flex items-center space-x-4 border-l border-slate-600 pl-4">
                <div className="flex items-center space-x-2 text-slate-300">
                  <User className="w-4 h-4" />
                  <span className="text-sm font-medium">{user?.name || user?.username}</span>
                </div>
                <button
                  onClick={logout}
                  className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-slate-300 hover:text-red-400 border border-transparent hover:border-red-400/30 rounded-lg transition-all duration-200"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4 mr-1" />
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-slate-800/95 backdrop-blur-sm border-t border-slate-700 z-50 shadow-lg">
        <div className="grid grid-cols-3 h-16">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex flex-col items-center justify-center space-y-1 transition-all duration-300 active:scale-95',
                  isActive
                    ? 'text-indigo-400 glow'
                    : 'text-slate-400 hover:text-slate-200 active:text-indigo-400'
                )}
              >
                <Icon className="w-5 h-5" />
                <span className="text-xs font-medium truncate">{item.label}</span>
              </Link>
            );
          })}
        </div>
      </nav>
    </>
  );
};

export default Navigation;