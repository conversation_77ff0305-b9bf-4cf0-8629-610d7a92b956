# 🚰 RO Service Manager

A comprehensive water purifier service management application built with Next.js, MongoDB, and modern web technologies.

## ✨ Features

- **Client Management**: Add, edit, and manage RO service clients
- **Service Scheduling**: Recurring and one-time scheduled services
- **Dashboard**: Real-time overview of all services (overdue, today, upcoming)
- **Service Completion**: Interactive service completion with recurring conversion
- **Mobile PWA**: Installable progressive web app with offline capabilities
- **Authentication**: Secure login system with JWT tokens
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Backend**: Next.js API Routes
- **Database**: MongoDB with Mongoose ODM
- **Styling**: Tailwind CSS with custom futuristic theme
- **Icons**: Lucide React
- **Authentication**: Custom JWT-based authentication
- **PWA**: Service Worker + Web App Manifest

## 📁 Project Structure

```
ro-service-app/
├── backend/
│   ├── models/          # Database models
│   ├── routes/          # API routes
│   │   └── api/         # REST API endpoints
│   ├── controllers/      # Business logic
│   ├── services/        # Service layer
│   ├── middleware/      # Middleware functions
│   ├── utils/           # Utility functions
│   ├── scripts/         # Database and utility scripts
│   └── config/          # Configuration files
├── frontend/
│   ├── components/      # React components
│   ├── contexts/        # React contexts
│   ├── pages/           # Next.js pages
│   ├── styles/          # CSS and styling files
│   ├── assets/          # Static assets (images, icons, etc.)
│   └── config/          # Frontend configuration
├── public/              # Public static files
├── node_modules/        # Dependencies
├── server.js            # Main server entry point
├── next.config.ts       # Next.js configuration
├── package.json         # Project dependencies and scripts
└── ...                  # Other configuration files
```

## Backend Structure

The backend contains all server-side code including:
- Database models (Mongoose schemas)
- API routes and controllers
- Business logic services
- Utility functions
- Database scripts for initialization and maintenance

## Frontend Structure

The frontend contains all client-side code including:
- React components
- Next.js pages
- Context providers for state management
- Styling files
- Static assets

## Available Scripts

In the project directory, you can run:

### `npm run dev`

Runs the app in development mode.
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### `npm run build`

Builds the app for production to the `.next` folder.

### `npm start`

Runs the production build of the app.

### Database Scripts

- `npm run db:init` - Initialize the database
- `npm run db:seed` - Seed the database with sample data
- `npm run db:stats` - Show database statistics
- `npm run db:optimize` - Optimize database performance
- `npm run db:update-admin` - Update admin user
- `npm run db:reset-admin` - Reset admin user
- `npm run db:add-test-services` - Add test completed services

## Deployment

The application is configured for deployment with Docker. Use the provided Dockerfile and docker-compose.yml for containerized deployment.

## Learn More

This project uses:
- [Next.js](https://nextjs.org/) for the frontend
- [MongoDB](https://www.mongodb.com/) for the database
- [Mongoose](https://mongoosejs.com/) for object modeling
- [Tailwind CSS](https://tailwindcss.com/) for styling

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- MongoDB database (local or cloud)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd ro-service-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.production .env.local
   ```
   
   Edit `.env.local` with your configuration:
   ```env
   # Database Configuration (Required)
   MONGODB_URI=mongodb://localhost:27017/ro-service-app
   
   # Authentication Configuration (Required)
   NEXTAUTH_SECRET=your-super-secret-key-minimum-32-characters
   NEXTAUTH_URL=http://localhost:3000
   
   # Application Configuration
   NODE_ENV=development
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Default Login Credentials

- **Username**: `ketan`
- **Password**: `radhe@1133`

## 🗄️ Database Setup

### Local MongoDB

1. **Install MongoDB**
   - Download from [mongodb.com](https://www.mongodb.com/try/download/community)
   - Follow installation instructions for your OS

2. **Start MongoDB**
   ```bash
   # Windows
   net start MongoDB
   
   # macOS/Linux
   sudo systemctl start mongod
   ```

3. **Update connection string**
   ```env
   MONGODB_URI=mongodb://localhost:27017/ro-service-app
   ```

### MongoDB Atlas (Cloud)

1. **Create account** at [mongodb.com/atlas](https://www.mongodb.com/atlas)
2. **Create cluster** and database
3. **Get connection string** from Atlas dashboard
4. **Update environment variable**
   ```env
   MONGODB_URI=mongodb+srv://username:<EMAIL>/ro-service-app
   ```

## 📱 PWA Installation

The app can be installed on mobile devices as a Progressive Web App:

### Android/Chrome
1. Visit the website in Chrome
2. Tap "Install App" prompt
3. App will be added to home screen

### iPhone/Safari
1. Visit the website in Safari
2. Tap Share button → "Add to Home Screen"
3. App will be added to home screen

## 🚀 Deployment

### Vercel (Recommended)

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Set environment variables in Vercel dashboard
   - Deploy automatically

3. **Environment Variables in Vercel**
   ```
   MONGODB_URI=your-mongodb-connection-string
   NEXTAUTH_SECRET=your-secret-key
   NEXTAUTH_URL=https://your-app.vercel.app
   ```

### Other Platforms

#### Netlify
```bash
npm run build
# Deploy 'out' folder to Netlify
```

#### Traditional Server
```bash
npm run build
npm start
```

#### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
npm run db:seed      # Seed database with sample data (if available)
```

### Adding New Features

1. **API Routes**: Add to `src/app/api/`
2. **Components**: Add to `components/`
3. **Database Models**: Add to `models/`
4. **Utilities**: Add to `utils/`
5. **Styles**: Add to `styles/` or use Tailwind classes

### Code Style

- Use TypeScript for type safety
- Follow Next.js conventions
- Use Tailwind CSS for styling
- Implement proper error handling
- Add loading states for better UX

## 🔒 Security

- JWT-based authentication
- Input validation on all forms
- MongoDB injection protection
- XSS protection headers
- CSRF protection (built-in Next.js)
- Secure environment variable handling

## 📊 Performance

- Server-side rendering with Next.js
- Optimized images and assets
- Database connection pooling
- Efficient MongoDB queries with indexes
- PWA caching for offline functionality

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Check if MongoDB is running
   - Verify connection string in `.env.local`
   - Ensure network access for cloud databases

2. **Build Errors**
   - Clear `.next` folder: `rm -rf .next`
   - Reinstall dependencies: `rm -rf node_modules && npm install`
   - Check TypeScript errors: `npm run lint`

3. **Authentication Issues**
   - Verify `NEXTAUTH_SECRET` is set
   - Check browser localStorage for tokens
   - Ensure correct login credentials

### Getting Help

1. Check the console for error messages
2. Review the logs in development mode
3. Verify environment variables are set correctly
4. Ensure database connection is working

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Check the troubleshooting section
- Review the documentation
- Open an issue on GitHub

---

**Built with ❤️ for efficient RO service management**