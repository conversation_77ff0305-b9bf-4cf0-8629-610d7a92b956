import { z } from 'zod';

// Environment validation schema
const envSchema = z.object({
  // Database
  MONGODB_URI: z.string().min(1, 'MONGODB_URI is required'),
  
  // Authentication
  NEXTAUTH_SECRET: z.string().min(32, 'NEXTAUTH_SECRET must be at least 32 characters').optional(),
  NEXTAUTH_URL: z.string().url('NEXTAUTH_URL must be a valid URL').optional(),
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters').optional(),
  
  // Application
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  NEXT_TELEMETRY_DISABLED: z.string().optional(),
  
  // Optional services
  NEXT_PUBLIC_API_URL: z.string().url().optional(),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // Production-specific
  FORCE_HTTPS: z.string().optional(),
  ALLOWED_ORIGINS: z.string().optional(),
  RATE_LIMIT_WINDOW_MS: z.string().optional(),
  RATE_LIMIT_MAX_REQUESTS: z.string().optional(),
});

export type Env = z.infer<typeof envSchema>;

/**
 * Validates environment variables and returns typed configuration
 * Throws an error if validation fails
 * Works differently on client vs server side
 */
export function validateEnv(): Env {
  // On client side, only validate what's available
  if (typeof window !== 'undefined') {
    // Client-side validation - only validate public env vars
    const clientEnv = {
      NODE_ENV: process.env.NODE_ENV || 'development',
      NEXT_TELEMETRY_DISABLED: process.env.NEXT_TELEMETRY_DISABLED,
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      LOG_LEVEL: 'info',
      // Use defaults for required server-only vars on client
      MONGODB_URI: 'client-side-placeholder',
      NEXTAUTH_SECRET: 'client-side-placeholder-32-characters-long',
      NEXTAUTH_URL: typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000',
      JWT_SECRET: 'client-side-placeholder-32-characters-long',
    };
    
    return clientEnv as Env;
  }
  
  // Server-side validation - validate all env vars
  try {
    console.log('Validating environment variables...');
    console.log('Available env vars:', Object.keys(process.env).filter(key => 
      ['MONGODB_URI', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL', 'JWT_SECRET', 'NODE_ENV'].includes(key)
    ).map(key => `${key}: ${process.env[key] ? 'Present' : 'Missing'}`));
    
    // For production, make some variables optional but log warnings
    if (process.env.NODE_ENV === 'production') {
      console.log('Production environment detected - applying flexible validation');
      
      // Create a more flexible schema for production
      const flexibleEnvSchema = z.object({
        // Database - still required
        MONGODB_URI: z.string().min(1, 'MONGODB_URI is required'),
        
        // Authentication - at least one is required
        NEXTAUTH_SECRET: z.string().min(32, 'NEXTAUTH_SECRET must be at least 32 characters').optional(),
        NEXTAUTH_URL: z.string().url('NEXTAUTH_URL must be a valid URL').optional(),
        JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters').optional(),
        
        // Application
        NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
        NEXT_TELEMETRY_DISABLED: z.string().optional(),
        
        // Optional services
        NEXT_PUBLIC_API_URL: z.string().url().optional(),
        LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
        
        // Production-specific
        FORCE_HTTPS: z.string().optional(),
        ALLOWED_ORIGINS: z.string().optional(),
        RATE_LIMIT_WINDOW_MS: z.string().optional(),
        RATE_LIMIT_MAX_REQUESTS: z.string().optional(),
      });
      
      const env = flexibleEnvSchema.parse(process.env);
      
      // Additional validation for production
      if (!env.JWT_SECRET && !env.NEXTAUTH_SECRET) {
        console.warn('⚠️  Warning: Neither JWT_SECRET nor NEXTAUTH_SECRET is set in production');
      }
      
      if (env.NEXTAUTH_URL && env.NEXTAUTH_URL.includes('localhost')) {
        console.warn('⚠️  Warning: Using localhost URL in production environment');
      }
      
      if (env.MONGODB_URI.includes('localhost')) {
        console.warn('⚠️  Warning: Using localhost MongoDB in production environment');
      }
      
      return env;
    }
    
    // Development/validation mode - strict validation
    const env = envSchema.parse(process.env);
    
    // Additional validation for production
    if (env.NODE_ENV === 'production') {
      if (!env.JWT_SECRET) {
        throw new Error('JWT_SECRET is required in production');
      }
      
      if (env.NEXTAUTH_URL && env.NEXTAUTH_URL.includes('localhost')) {
        console.warn('⚠️  Warning: Using localhost URL in production environment');
      }
      
      if (env.MONGODB_URI.includes('localhost')) {
        console.warn('⚠️  Warning: Using localhost MongoDB in production environment');
      }
    }
    
    return env;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      console.error('Environment validation failed:', errorMessages.join('\n'));
      throw new Error(`Environment validation failed:\n${errorMessages.join('\n')}`);
    }
    console.error('Environment validation error:', error);
    throw error;
  }
}

/**
 * Gets validated environment configuration
 * Caches the result to avoid repeated validation
 */
let cachedEnv: Env | null = null;

export function getEnv(): Env {
  if (!cachedEnv) {
    cachedEnv = validateEnv();
  }
  return cachedEnv;
}

/**
 * Checks if all required environment variables are set
 * Returns an array of missing variables
 */
export function checkRequiredEnvVars(): string[] {
  const required = ['MONGODB_URI'];
  const missing: string[] = [];
  
  for (const key of required) {
    if (!process.env[key]) {
      missing.push(key);
    }
  }
  
  // Check for at least one authentication secret
  if (!process.env.JWT_SECRET && !process.env.NEXTAUTH_SECRET) {
    missing.push('JWT_SECRET or NEXTAUTH_SECRET');
  }
  
  return missing;
}

/**
 * Logs environment configuration (without sensitive data)
 */
export function logEnvConfig(): void {
  try {
    const env = getEnv();
    
    console.log('🔧 Environment Configuration:');
    console.log(`   NODE_ENV: ${env.NODE_ENV}`);
    console.log(`   NEXTAUTH_URL: ${env.NEXTAUTH_URL || 'Not set'}`);
    console.log(`   Database: ${env.MONGODB_URI.includes('localhost') ? 'Local MongoDB' : 'Remote MongoDB'}`);
    console.log(`   Log Level: ${env.LOG_LEVEL}`);
    console.log(`   Auth Secret: ${env.JWT_SECRET ? 'JWT_SECRET set' : env.NEXTAUTH_SECRET ? 'NEXTAUTH_SECRET set' : 'None set'}`);
    
    if (env.NODE_ENV === 'production') {
      console.log('✅ Production environment detected');
    } else {
      console.log('🔧 Development environment detected');
    }
  } catch (error) {
    console.error('Error logging environment configuration:', error);
  }
}