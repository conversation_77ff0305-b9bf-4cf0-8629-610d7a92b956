{"name": "RO Service Manager", "short_name": "RO Manager", "description": "Professional RO service management application for scheduling and tracking water purifier services", "start_url": "/", "display": "fullscreen", "orientation": "portrait-primary", "theme_color": "#0ea5e9", "background_color": "#0f172a", "categories": ["business", "productivity", "utilities"], "lang": "en", "scope": "/", "icons": [{"src": "/favicon.ico", "sizes": "48x48", "type": "image/x-icon"}, {"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'%3E%3Cdefs%3E%3ClinearGradient id='g' x1='0' y1='0' x2='1' y2='1'%3E%3Cstop offset='0' stop-color='%230ea5e9'/%3E%3Cstop offset='1' stop-color='%230369a1'/%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='192' height='192' rx='29' fill='url(%23g)'/%3E%3Cpath d='M96 67c-8 0-15 3-20 8s-8 12-8 20c0 16 13 29 28 29s28-13 28-29c0-8-3-15-8-20s-12-8-20-8z' fill='white'/%3E%3Ctext x='96' y='145' font-family='Arial' font-size='24' font-weight='bold' text-anchor='middle' fill='white'%3ERO%3C/text%3E%3C/svg%3E", "sizes": "192x192", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cdefs%3E%3ClinearGradient id='g' x1='0' y1='0' x2='1' y2='1'%3E%3Cstop offset='0' stop-color='%230ea5e9'/%3E%3Cstop offset='1' stop-color='%230369a1'/%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='512' height='512' rx='77' fill='url(%23g)'/%3E%3Cpath d='M256 179c-21 0-40 8-54 21s-21 32-21 53c0 42 35 77 75 77s75-35 75-77c0-21-8-40-21-53s-33-21-54-21z' fill='white'/%3E%3Ctext x='256' y='387' font-family='Arial' font-size='64' font-weight='bold' text-anchor='middle' fill='white'%3ERO%3C/text%3E%3C/svg%3E", "sizes": "512x512", "type": "image/svg+xml", "purpose": "maskable any"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "View service dashboard", "url": "/", "icons": [{"src": "/favicon.ico", "sizes": "48x48"}]}, {"name": "Add Client", "short_name": "Add Client", "description": "Add new client", "url": "/clients/new", "icons": [{"src": "/favicon.ico", "sizes": "48x48"}]}, {"name": "All Clients", "short_name": "Clients", "description": "View all clients", "url": "/clients", "icons": [{"src": "/favicon.ico", "sizes": "48x48"}]}]}