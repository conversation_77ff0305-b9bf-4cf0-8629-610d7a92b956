'use client';

import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error | undefined; resetError: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false });
  };

  override render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
      }

      return (
        <div className="min-h-screen flex items-center justify-center px-4">
          <div className="text-center futuristic-card p-6 sm:p-8 max-w-md w-full">
            <AlertTriangle className="w-12 h-12 sm:w-16 sm:h-16 text-danger mx-auto mb-4" />
            <h2 className="text-xl sm:text-2xl font-bold text-foreground mb-2">Something went wrong</h2>
            <p className="text-sm sm:text-base text-foreground/70 mb-4 sm:mb-6">
              We encountered an unexpected error. Please try refreshing the page.
            </p>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="bg-danger/20 border border-danger/30 rounded-md p-3 mb-4 text-left">
                <p className="text-xs text-danger font-mono break-all">
                  {this.state.error.message}
                </p>
              </div>
            )}
            <button
              onClick={this.resetError}
              className="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors glow text-sm sm:text-base"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;