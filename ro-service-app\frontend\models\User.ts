import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser {
  _id: string;
  username: string;
  email?: string;
  password: string;
  name: string;
  role: 'admin' | 'user';
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserMethods {
  comparePassword(candidatePassword: string): Promise<boolean>;
  toSafeObject(): Omit<IUser, 'password'>;
}

export interface IUserModel extends mongoose.Model<IUser, {}, IUserMethods> {
  findByUsername(username: string): Promise<(IUser & IUserMethods) | null>;
  createUser(userData: {
    username: string;
    password: string;
    name: string;
    email?: string;
    role?: 'admin' | 'user';
  }): Promise<IUser & IUserMethods>;
}

const UserSchema = new mongoose.Schema<IUser, IUserModel, IUserMethods>({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    lowercase: true,
    minlength: [3, 'Username must be at least 3 characters'],
    maxlength: [30, 'Username cannot exceed 30 characters'],
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email address'],
    sparse: true // Allows multiple null values but enforces uniqueness for non-null values
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters']
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  role: {
    type: String,
    enum: ['admin', 'user'],
    default: 'user'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(_doc, ret) {
      const { password, ...safeRet } = ret;
      return safeRet;
    }
  }
});

// Optimized indexes for performance
// Unique index for username (primary login field)
UserSchema.index({ username: 1 }, { unique: true });

// Sparse unique index for email (optional field)
UserSchema.index({ email: 1 }, { unique: true, sparse: true });

// Compound index for active user queries
UserSchema.index({ isActive: 1, role: 1 });

// Index for login tracking
UserSchema.index({ lastLogin: -1 }, { sparse: true });

// Index for user management queries
UserSchema.index({ role: 1, createdAt: -1 });

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Instance method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Instance method to return safe user object (without password)
UserSchema.methods.toSafeObject = function() {
  const userObject = this.toObject();
  const { password, ...safeObject } = userObject;
  return safeObject;
};

// Static method to find user by username
UserSchema.statics.findByUsername = function(username: string) {
  return this.findOne({ username: username.toLowerCase(), isActive: true });
};

// Static method to create user with hashed password
UserSchema.statics.createUser = async function(userData: {
  username: string;
  password: string;
  name: string;
  email?: string;
  role?: 'admin' | 'user';
}) {
  // Validate password strength
  if (userData.password.length < 8) {
    throw new Error('Password must be at least 8 characters long');
  }
  
  // Check if username already exists
  const existingUser = await this.findOne({ username: userData.username.toLowerCase() });
  if (existingUser) {
    throw new Error('Username already exists');
  }
  
  // Check if email already exists (if provided)
  if (userData.email) {
    const existingEmail = await this.findOne({ email: userData.email.toLowerCase() });
    if (existingEmail) {
      throw new Error('Email already exists');
    }
  }
  
  return this.create({
    ...userData,
    username: userData.username.toLowerCase(),
    email: userData.email?.toLowerCase()
  });
};

// Password exclusion is already handled in the schema options above

const User = mongoose.models.User || mongoose.model<IUser, IUserModel>('User', UserSchema);

export default User;
