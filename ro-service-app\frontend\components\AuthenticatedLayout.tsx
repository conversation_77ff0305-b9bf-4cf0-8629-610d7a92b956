'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from "@/components/AuthContext";
import Navigation from "@/components/Navigation";
import DailyNotification from "@/components/DailyNotification";
import PWAInstall from "@/components/PWAInstall";
import ProtectedRoute from "@/components/ProtectedRoute";

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

const AuthenticatedLayout: React.FC<AuthenticatedLayoutProps> = ({ children }) => {
  const pathname = usePathname();
  const { isAuthenticated, isLoading } = useAuth();
  
  // Don't show navigation on login page
  const isLoginPage = pathname === '/login';
  
  if (isLoginPage) {
    return <>{children}</>;
  }
  
  if (isLoading) {
    return <>{children}</>;
  }
  
  if (!isAuthenticated) {
    return <>{children}</>;
  }
  
  return (
    <div className="min-h-screen flex flex-col w-full max-w-full overflow-x-hidden">
      <Navigation />
      <main className="flex-grow pb-16 md:pb-0 w-full max-w-full overflow-x-hidden">
        <ProtectedRoute>
          {children}
        </ProtectedRoute>
      </main>
      <DailyNotification />
      <PWAInstall />
    </div>
  );
};

export default AuthenticatedLayout;