import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Client from '@/models/Client';
import mongoose from 'mongoose';

export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid client ID' },
        { status: 400 }
      );
    }

    // Find the client first to ensure it exists
    const client = await (Client as any).findById(id);

    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    // Update only the service type and interval for recurring service
    client.serviceType = 'recurring';
    client.serviceInterval = 3;
    // Clear scheduledDate for recurring services
    client.scheduledDate = null;

    // Save the client to trigger pre-validate hook for nextServiceDate calculation
    await client.save();

    console.log('Client converted to recurring service:', {
      id: client._id,
      serviceType: client.serviceType,
      serviceInterval: client.serviceInterval,
      nextServiceDate: client.nextServiceDate
    });

    return NextResponse.json({
      success: true,
      data: client,
      message: 'Client successfully converted to 3-month recurring service'
    });
  } catch (error) {
    console.error('Error converting client to recurring service:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to convert client to recurring service' },
      { status: 500 }
    );
  }
}