'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON>O<PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>ader2, User, Lock } from 'lucide-react';
import ErrorBoundary from '@/components/ErrorBoundary';
import { clientLogin, storeUserData } from '@/utils/auth';

interface LoginFormData {
    username: string;
    password: string;
}

interface LoginErrors {
    username?: string;
    password?: string;
    general?: string;
}

const LoginPageContent = () => {
    const [formData, setFormData] = useState<LoginFormData>({
        username: '',
        password: ''
    });
    const [errors, setErrors] = useState<LoginErrors>({});
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const validateForm = (): boolean => {
        const newErrors: LoginErrors = {};

        // Username validation
        if (!formData.username.trim()) {
            newErrors.username = 'Userna<PERSON> is required';
        } else if (formData.username.trim().length < 3) {
            newErrors.username = 'Username must be at least 3 characters long';
        }

        // Password validation
        if (!formData.password) {
            newErrors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            newErrors.password = 'Password must be at least 6 characters long';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));

        // Clear error for this field when user starts typing
        if (errors[name as keyof LoginErrors]) {
            setErrors(prev => ({ ...prev, [name]: undefined }));
        }
        // Clear general error when user starts typing
        if (errors.general) {
            setErrors(prev => {
                const { general, ...rest } = prev;
                return rest;
            });
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setLoading(true);
        setErrors({});

        try {
            const result = await clientLogin(formData.username, formData.password);

            if (result.success && result.token && result.user) {
                // Store authentication token/session
                storeUserData(result.user, result.token);

                // Force page reload to trigger auth context
                window.location.href = '/';
            } else {
                setErrors({ general: result.error || 'Invalid username or password' });
            }
        } catch (error) {
            console.error('Login error:', error);
            setErrors({ general: 'Login failed. Please try again.' });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-slate-900 flex items-center justify-center px-4 py-8">
            <div className="w-full max-w-md">
                {/* Header */}
                <div className="text-center mb-8">
                    <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 glow">
                        <span className="text-white font-bold text-2xl">RO</span>
                    </div>
                    <h1 className="text-3xl font-bold gradient-text mb-2">Welcome Back</h1>
                    <p className="text-slate-400">Sign in to RO Service Manager</p>
                </div>

                {/* Login Form */}
                <div className="futuristic-card p-6 sm:p-8">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* General Error */}
                        {errors.general && (
                            <div className="bg-red-900/20 border border-red-500/30 rounded-md p-4">
                                <div className="flex items-start">
                                    <AlertTriangle className="w-5 h-5 text-red-400 mr-3 flex-shrink-0 mt-0.5" />
                                    <div className="text-sm text-red-400">{errors.general}</div>
                                </div>
                            </div>
                        )}

                        {/* Username Field */}
                        <div>
                            <label htmlFor="username" className="block text-sm font-medium text-slate-200 mb-2">
                                Username
                            </label>
                            <div className="relative">
                                <User className="h-5 w-5 text-slate-400 input-icon-left" />
                                <input
                                    type="text"
                                    id="username"
                                    name="username"
                                    value={formData.username}
                                    onChange={handleInputChange}
                                    className={`input-field input-with-icon-left ${errors.username ? 'border-red-500' : ''}`}
                                    placeholder="Enter your username"
                                    disabled={loading}
                                />
                            </div>
                            {errors.username && <p className="mt-2 text-sm text-red-400">{errors.username}</p>}
                        </div>

                        {/* Password Field */}
                        <div>
                            <label htmlFor="password" className="block text-sm font-medium text-slate-200 mb-2">
                                Password
                            </label>
                            <div className="relative">
                                <Lock className="h-5 w-5 text-slate-400 input-icon-left" />
                                <input
                                    type={showPassword ? 'text' : 'password'}
                                    id="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    className={`input-field input-with-icon-left input-with-icon-right ${errors.password ? 'border-red-500' : ''}`}
                                    placeholder="Enter your password"
                                    disabled={loading}
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="input-icon-right text-slate-400 hover:text-slate-200 transition-colors"
                                    disabled={loading}
                                >
                                    {showPassword ? (
                                        <EyeOff className="h-5 w-5" />
                                    ) : (
                                        <Eye className="h-5 w-5" />
                                    )}
                                </button>
                            </div>
                            {errors.password && <p className="mt-2 text-sm text-red-400">{errors.password}</p>}
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            disabled={loading}
                            className="btn-primary w-full py-3"
                        >
                            {loading ? (
                                <div className="flex items-center justify-center">
                                    <Loader2 className="animate-spin h-5 w-5 mr-2" />
                                    <span>Signing In...</span>
                                </div>
                            ) : (
                                'Sign In'
                            )}
                        </button>
                    </form>


                </div>

                {/* Footer */}
                <div className="text-center mt-8">
                    <p className="text-sm text-slate-500">
                        RO Service Manager © 2024
                    </p>
                </div>
            </div>
        </div>
    );
};

const LoginPage = () => {
    return (
        <ErrorBoundary>
            <LoginPageContent />
        </ErrorBoundary>
    );
};

export default LoginPage;