'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function TestButtonsPage() {
  const router = useRouter();
  const [clients, setClients] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/clients');
        const result = await response.json();

        if (result.success) {
          setClients(result.data);
        } else {
          setError(result.error || 'Failed to fetch clients');
        }
      } catch (err) {
        setError('Failed to fetch clients');
        console.error('Clients fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchClients();
  }, []);

  const testDelete = async (clientId: string) => {
    try {
      console.log('Attempting to delete client:', clientId);
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'DELETE',
      });
      
      const result = await response.json();
      console.log('Delete response:', result);
      
      if (result.success) {
        // Remove client from state
        setClients(prev => prev.filter(client => client._id !== clientId));
        alert('Client deleted successfully');
      } else {
        alert('Failed to delete client: ' + (result.error || 'Unknown error'));
      }
    } catch (err) {
      console.error('Delete error:', err);
      alert('Failed to delete client: ' + (err as Error).message);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-slate-100 mb-2">Error Loading Clients</h2>
          <p className="text-slate-400 mb-4">{error}</p>
          <Link href="/clients" className="btn-primary">
            Back to Clients
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 px-4 sm:px-6 lg:px-8 py-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <Link href="/clients" className="btn-primary mb-4 inline-block">
            Back to Clients
          </Link>
          <h1 className="text-2xl font-bold text-slate-200 mb-2">Test Edit/Delete Buttons</h1>
          <p className="text-slate-400">This page helps test the edit and delete functionality</p>
        </div>

        <div className="space-y-4">
          {clients.map((client) => (
            <div key={client._id} className="futuristic-card p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1 min-w-0 pr-3">
                  <h3 className="text-lg font-semibold text-slate-100 mb-1">{client.name}</h3>
                  <p className="text-sm text-slate-400 mb-1">{client.phone}</p>
                  <p className="text-sm text-slate-400">{client.location}</p>
                </div>
                <div className="flex gap-2 flex-shrink-0">
                  <Link
                    href={`/clients/${client._id}`}
                    className="p-2 text-slate-400 hover:text-cyan-400 hover:bg-slate-700 rounded-lg transition-colors"
                  >
                    Edit
                  </Link>
                  <button
                    onClick={() => testDelete(client._id)}
                    className="p-2 text-slate-400 hover:text-red-400 hover:bg-slate-700 rounded-lg transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}