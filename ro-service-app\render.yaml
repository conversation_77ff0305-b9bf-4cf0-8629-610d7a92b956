services:
  - type: web
    name: ro-service-app
    env: node
    plan: free
    buildCommand: cd frontend && npm install && npm run build
    startCommand: NODE_ENV=production node server.js
    envVars:
      - key: NODE_ENV
        value: production
      - key: NEXT_TELEMETRY_DISABLED
        value: 1
      - key: MONGODB_URI
        sync: false
      - key: NEXTAUTH_SECRET
        sync: false
      - key: NEXTAUTH_URL
        sync: false
      - key: JWT_SECRET
        sync: false