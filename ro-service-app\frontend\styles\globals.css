@import "tailwindcss";

@theme {
  /* Professional Dark Theme Color Palette */
  --color-primary: #6366f1;
  --color-primary-dark: #4f46e5;
  --color-primary-light: #818cf8;
  
  --color-secondary: #64748b;
  --color-secondary-dark: #475569;
  --color-secondary-light: #94a3b8;
  
  --color-accent: #06b6d4;
  --color-accent-dark: #0891b2;
  --color-accent-light: #22d3ee;
  
  --color-danger: #ef4444;
  --color-danger-dark: #dc2626;
  --color-danger-light: #f87171;
  
  --color-warning: #f59e0b;
  --color-warning-dark: #d97706;
  --color-warning-light: #fbbf24;
  
  --color-success: #10b981;
  --color-success-dark: #059669;
  --color-success-light: #34d399;
  
  /* Dark Theme Background Colors */
  --color-background: #0f172a;
  --color-foreground: #f1f5f9;
  --color-card-bg: #1e293b;
  --color-card-border: #334155;
  --color-muted: #64748b;
  --color-muted-foreground: #94a3b8;
  
  /* Surface Colors */
  --color-surface: #1e293b;
  --color-surface-hover: #334155;
  --color-border: #334155;
  --color-input: #334155;
  
  --font-family-sans: var(--font-inter), system-ui, sans-serif;
}

:root {
  --background: #0f172a;
  --foreground: #f1f5f9;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  /* Disable zoom functionality */
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Dark theme background */
  background-color: #0f172a;
  color: #f1f5f9;
}

/* Prevent zoom on input focus for iOS */
input, textarea, select {
  font-size: 16px !important;
}

/* Disable zoom gestures */
html {
  touch-action: manipulation;
}

/* Mobile-first responsive utilities */
.mobile-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Touch-friendly button sizing */
@media (max-width: 768px) {
  button, .btn {
    min-height: 44px;
    min-width: 44px;
  }

  input, textarea, select {
    min-height: 44px;
  }
}

/* Improved focus states for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Dark Theme Component Classes */
.futuristic-card {
  background-color: #1e293b;
  border: 1px solid #334155;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease-in-out;
}

.futuristic-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  border-color: #475569;
}

.gradient-text {
  background: linear-gradient(135deg, #6366f1 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  transition: box-shadow 0.2s ease-in-out;
}

.glow:hover {
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
}

.btn-primary {
  background-color: #6366f1;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
  background-color: #334155;
  color: #f1f5f9;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: 1px solid #475569;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: #475569;
  border-color: #64748b;
}

.btn-success {
  background-color: #10b981;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
}

.btn-success:hover {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-edit {
  background-color: #06b6d4;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
}

.btn-edit:hover {
  background-color: #0891b2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.4);
}

.input-field {
  width: 100%;
  padding: 0.875rem 1rem;
  background-color: #334155;
  border: 1px solid #475569;
  border-radius: 0.5rem;
  color: #f1f5f9;
  transition: all 0.2s ease-in-out;
  font-size: 16px;
  line-height: 1.5;
  min-height: 48px;
}

.input-field:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background-color: #3f4a5a;
}

.input-field::placeholder {
  color: #94a3b8;
  opacity: 0.7;
}

.input-field:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* Status badges */
.status-overdue {
  background-color: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-today {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fcd34d;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-upcoming {
  background-color: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Navigation styles */
.nav-item {
  color: #94a3b8;
  transition: all 0.2s ease-in-out;
}

.nav-item:hover {
  color: #f1f5f9;
}

.nav-item.active {
  color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
}

/* Input field with icon spacing */
.input-with-icon-left {
  padding-left: 3.5rem !important;
}

.input-with-icon-right {
  padding-right: 3.5rem !important;
}

.input-icon-left {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 10;
}

.input-icon-right {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  cursor: pointer;
}

/* Dashboard specific styles */
.dashboard-stat-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border: 1px solid #475569;
  border-radius: 1rem;
  padding: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

@media (min-width: 640px) {
  .dashboard-stat-card {
    padding: 1.5rem;
  }
}

.dashboard-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-stat-card:hover::before {
  opacity: 1;
}

.dashboard-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  border-color: #64748b;
}

.pulse-dot {
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* Notification styles */
.notification-success {
  background-color: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #6ee7b7;
}

.notification-error {
  background-color: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

.notification-warning {
  background-color: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #fcd34d;
}

.notification-info {
  background-color: rgba(6, 182, 212, 0.2);
  border: 1px solid rgba(6, 182, 212, 0.3);
  color: #22d3ee;
}