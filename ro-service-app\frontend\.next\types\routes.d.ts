// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/clients" | "/clients/[id]" | "/clients/new" | "/login"
type AppRouteHandlerRoutes = "/api/auth/login" | "/api/auth/logout" | "/api/clients" | "/api/clients/[id]" | "/api/clients/[id]/convert-to-recurring" | "/api/dashboard" | "/api/services" | "/api/services/[id]/complete"
type PageRoutes = never
type LayoutRoutes = "/" | "/clients"
type RedirectRoutes = never
type RewriteRoutes = "/api/[[...path]]"
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/api/[[...path]]": { "path"?: string[]; }
  "/api/auth/login": {}
  "/api/auth/logout": {}
  "/api/clients": {}
  "/api/clients/[id]": { "id": string; }
  "/api/clients/[id]/convert-to-recurring": { "id": string; }
  "/api/dashboard": {}
  "/api/services": {}
  "/api/services/[id]/complete": { "id": string; }
  "/clients": {}
  "/clients/[id]": { "id": string; }
  "/clients/new": {}
  "/login": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
  "/clients": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
