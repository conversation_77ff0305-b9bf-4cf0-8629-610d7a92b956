import mongoose from 'mongoose';

const ServiceSchema = new mongoose.Schema({
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client',
    required: [true, 'Client ID is required']
  },
  serviceDate: {
    type: Date,
    required: [true, 'Service date is required'],
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'cancelled'],
    default: 'pending',
    required: [true, 'Service status is required']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot be more than 1000 characters']
  },
  completedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Optimized indexes for efficient queries
// Primary compound index for client service history
ServiceSchema.index({ clientId: 1, serviceDate: -1 });

// Index for status-based queries with date sorting
ServiceSchema.index({ status: 1, serviceDate: -1 });

// Index for date range queries
ServiceSchema.index({ serviceDate: 1, status: 1 });

// Index for completed services with completion date
ServiceSchema.index({ status: 1, completedAt: -1 }, {
  partialFilterExpression: { status: 'completed' }
});

// Compound index for dashboard analytics
ServiceSchema.index({ serviceDate: 1, clientId: 1, status: 1 });

// TTL index to automatically clean up old cancelled services (optional)
ServiceSchema.index({ createdAt: 1 }, {
  expireAfterSeconds: 365 * 24 * 60 * 60, // 1 year
  partialFilterExpression: { status: 'cancelled' }
});

export default mongoose.models.Service || mongoose.model('Service', ServiceSchema);