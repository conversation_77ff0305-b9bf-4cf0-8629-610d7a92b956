import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Client from '@/models/Client';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = parseInt(searchParams.get('skip') || '0');

    let query = {};

    if (search) {
      // Use text search for better performance when available
      if (search.length >= 3) {
        query = {
          $text: { $search: search }
        };
      } else {
        // Fallback to regex for short searches
        query = {
          $or: [
            { name: { $regex: search, $options: 'i' } },
            { location: { $regex: search, $options: 'i' } },
            { phone: { $regex: search, $options: 'i' } }
          ]
        };
      }
    }

    const clients = await (Client as any).find(query)
      .select('name phone location serviceType nextServiceDate lastServiceDate notes')
      .sort({ nextServiceDate: 1 })
      .skip(skip)
      .limit(limit)
      .lean();

    console.log('Clients list API - query results:', {
      searchTerm: search,
      clientCount: clients.length,
      sampleClients: clients.slice(0, 3).map((c: any) => ({
        id: c._id,
        name: c.name,
        serviceType: c.serviceType,
        nextServiceDate: c.nextServiceDate
      }))
    });

    return NextResponse.json({
      success: true,
      data: clients
    });
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch clients' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const body = await request.json();
    const { name, phone, location, notes, serviceType, serviceInterval, scheduledDate } = body;

    // Validate required fields
    if (!name || !phone || !location) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate service type specific fields
    if (serviceType === 'recurring' && !serviceInterval) {
      return NextResponse.json(
        { success: false, error: 'Service interval is required for recurring services' },
        { status: 400 }
      );
    }

    if (serviceType === 'scheduled' && !scheduledDate) {
      return NextResponse.json(
        { success: false, error: 'Scheduled date is required for scheduled services' },
        { status: 400 }
      );
    }

    // Check if client with same phone already exists
    const existingClient = await (Client as any).findOne({ phone });
    if (existingClient) {
      return NextResponse.json(
        { success: false, error: 'Client with this phone number already exists' },
        { status: 400 }
      );
    }

    const clientData: any = {
      name,
      phone,
      location,
      notes,
      serviceType: serviceType || 'recurring'
    };

    if (serviceType === 'scheduled') {
      // Normalize the scheduled date to UTC date without time
      const schedDate = new Date(scheduledDate);
      clientData.scheduledDate = new Date(Date.UTC(schedDate.getFullYear(), schedDate.getMonth(), schedDate.getDate()));
    } else {
      clientData.serviceInterval = serviceInterval || 3;
    }

    console.log('Client creation - data to save:', clientData);

    const client = new Client(clientData);
    await client.save();

    console.log('Client creation - saved client:', {
      id: client._id,
      serviceType: client.serviceType,
      nextServiceDate: client.nextServiceDate
    });

    return NextResponse.json({
      success: true,
      data: client
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating client:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create client' },
      { status: 500 }
    );
  }
}
