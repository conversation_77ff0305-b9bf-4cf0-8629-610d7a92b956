'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { CheckCircle, Repeat, X, Calendar } from 'lucide-react';

interface ServiceCompletionModalProps {
  isOpen: boolean;
  clientName: string;
  clientId: string;
  onClose: () => void;
  onCompleteOnly: () => void;
  onConvertToRecurring: () => void;
}

const ServiceCompletionModal = ({
  isOpen,
  clientName,
  clientId: _clientId,
  onClose,
  onCompleteOnly,
  onConvertToRecurring
}: ServiceCompletionModalProps) => {
  const [processing, setProcessing] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
    }

    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isOpen]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !mounted) {
    return null;
  }

  const handleCompleteOnly = async () => {
    setProcessing(true);
    try {
      await onCompleteOnly();
    } finally {
      setProcessing(false);
    }
  };

  const handleConvertToRecurring = async () => {
    setProcessing(true);
    try {
      await onConvertToRecurring();
    } finally {
      setProcessing(false);
    }
  };

  const modalContent = (
    <div 
      className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50 backdrop-blur-sm"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        className="bg-slate-800 rounded-xl p-6 w-full max-w-md border border-slate-700 shadow-2xl transform transition-all duration-300 scale-100"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          disabled={processing}
          className="absolute top-4 right-4 text-slate-400 hover:text-slate-200 transition-colors disabled:opacity-50"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-12 h-12 bg-indigo-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-6 h-6 text-indigo-400" />
          </div>
          <h2 className="text-xl font-bold text-slate-100 mb-2">
            Service Completion
          </h2>
          <p className="text-slate-400">
            How would you like to handle the service for <span className="font-semibold text-slate-200">{clientName}</span>?
          </p>
        </div>

        {/* Options */}
        <div className="space-y-4 mb-6">
          {/* Complete Only Option */}
          <button
            onClick={handleCompleteOnly}
            disabled={processing}
            className="w-full p-4 bg-slate-700/50 border border-slate-600 rounded-lg hover:border-green-500/50 hover:bg-green-500/10 transition-all text-left group disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <CheckCircle className="w-4 h-4 text-green-400" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-slate-100 mb-1">Complete Service Only</h3>
                <p className="text-sm text-slate-400">
                  Mark this service as completed. The client will remain in your system with no future scheduled services.
                </p>
              </div>
            </div>
          </button>

          {/* Convert to Recurring Option */}
          <button
            onClick={handleConvertToRecurring}
            disabled={processing}
            className="w-full p-4 bg-slate-700/50 border border-slate-600 rounded-lg hover:border-indigo-500/50 hover:bg-indigo-500/10 transition-all text-left group disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-indigo-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Repeat className="w-4 h-4 text-indigo-400" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-slate-100 mb-1">Convert to 3-Month Recurring Service</h3>
                <p className="text-sm text-slate-400">
                  Complete this service and automatically set up recurring services every 3 months.
                </p>
                <div className="flex items-center mt-2 text-xs text-indigo-400">
                  <Calendar className="w-3 h-3 mr-1 flex-shrink-0" />
                  <span>Next service: 3 months from today</span>
                </div>
              </div>
            </div>
          </button>
        </div>

        {/* Processing State */}
        {processing && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-400 mx-auto mb-3"></div>
            <p className="text-slate-400">Processing your request...</p>
          </div>
        )}

        {/* Cancel Button */}
        {!processing && (
          <button
            onClick={onClose}
            className="w-full py-3 text-slate-400 hover:text-slate-200 transition-colors text-sm font-medium rounded-lg hover:bg-slate-700/50"
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default ServiceCompletionModal;