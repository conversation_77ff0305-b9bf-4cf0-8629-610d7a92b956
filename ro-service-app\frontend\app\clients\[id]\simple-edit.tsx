'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function SimpleEditClientPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [clientId, setClientId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const resolveParams = async () => {
      try {
        const resolvedParams = await params;
        setClientId(resolvedParams.id);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };

    resolveParams();
  }, [params]);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-slate-100">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mx-auto mb-4"></div>
          <p>Loading client {clientId}...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 px-4 sm:px-6 lg:px-8 py-6">
      <div className="max-w-2xl mx-auto">
        <div className="futuristic-card p-6">
          <h1 className="text-2xl font-bold text-slate-100 mb-6">Edit Client</h1>
          <p className="text-slate-300 mb-4">Client ID: {clientId}</p>
          <div className="flex space-x-3">
            <Link href="/clients" className="btn-primary">
              Back to Clients
            </Link>
            <button 
              onClick={() => alert('Edit functionality would go here')}
              className="btn-secondary"
            >
              Test Edit
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}