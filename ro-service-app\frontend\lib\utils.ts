/**
 * Get today's date range in UTC
 * Returns start of day (00:00:00.000) and end of day (23:59:59.999) in UTC
 */
export function getTodayDateRange(): { start: Date; end: Date } {
  const today = new Date();
  const start = new Date(Date.UTC(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    0, 0, 0, 0
  ));
  
  const end = new Date(Date.UTC(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    23, 59, 59, 999
  ));
  
  return { start, end };
}

/**
 * Get tomorrow's date range in UTC
 * Returns start of day (00:00:00.000) and end of day (23:59:59.999) in UTC
 */
export function getTomorrowDateRange(): { start: Date; end: Date } {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const start = new Date(Date.UTC(
    tomorrow.getFullYear(),
    tomorrow.getMonth(),
    tomorrow.getDate(),
    0, 0, 0, 0
  ));
  
  const end = new Date(Date.UTC(
    tomorrow.getFullYear(),
    tomorrow.getMonth(),
    tomorrow.getDate(),
    23, 59, 59, 999
  ));
  
  return { start, end };
}