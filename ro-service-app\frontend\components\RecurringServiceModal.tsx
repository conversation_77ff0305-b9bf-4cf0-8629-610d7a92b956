'use client';

import { useState } from 'react';
import { X, Calendar, RefreshCw, CheckCircle } from 'lucide-react';

interface RecurringServiceModalProps {
  isOpen: boolean;
  clientName: string;
  clientId: string;
  onClose: () => void;
  onYes: (clientId: string) => void;
  onNo: () => void;
}

const RecurringServiceModal = ({
  isOpen,
  clientName,
  clientId,
  onClose,
  onYes,
  onNo
}: RecurringServiceModalProps) => {
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const handleYes = async () => {
    setIsProcessing(true);
    try {
      await onYes(clientId);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNo = () => {
    onNo();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50">
      <div className="futuristic-card max-w-md w-full p-6 relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-slate-400 hover:text-slate-200 transition-colors"
          disabled={isProcessing}
        >
          <X className="w-5 h-5" />
        </button>

        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 bg-emerald-500/20 rounded-full flex items-center justify-center mr-4">
            <CheckCircle className="w-6 h-6 text-emerald-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-slate-100">Service Completed!</h3>
            <p className="text-sm text-slate-400">for {clientName}</p>
          </div>
        </div>

        {/* Content */}
        <div className="mb-6">
          <div className="bg-indigo-900/20 border border-indigo-500/30 rounded-lg p-4 mb-4">
            <div className="flex items-start">
              <RefreshCw className="w-5 h-5 text-indigo-400 mr-3 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-slate-100 mb-1">
                  Set up recurring service?
                </h4>
                <p className="text-sm text-slate-300">
                  Would you like to set up automatic recurring service every 3 months?
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3 text-sm text-slate-400">
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2 text-slate-500" />
              <span>Automatic scheduling every 3 months</span>
            </div>
            <div className="flex items-center">
              <RefreshCw className="w-4 h-4 mr-2 text-slate-500" />
              <span>Never miss a service date</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 mr-2 text-slate-500" />
              <span>Easy to manage and modify</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={handleYes}
            disabled={isProcessing}
            className="btn-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Setting up...
              </>
            ) : (
              'Set Up 3-Month Recurring'
            )}
          </button>
          <button
            onClick={handleNo}
            disabled={isProcessing}
            className="btn-secondary flex-1 disabled:opacity-50"
          >
            Just Complete Service
          </button>
        </div>

        {/* Footer Note */}
        <p className="text-xs text-slate-500 text-center mt-4">
          You can always change this setting later in the client details
        </p>
      </div>
    </div>
  );
};

export default RecurringServiceModal;