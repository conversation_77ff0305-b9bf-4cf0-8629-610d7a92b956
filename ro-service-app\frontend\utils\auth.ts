// Frontend authentication utilities
import { User } from '@/utils/client-auth';

// Client-side login function
export const clientLogin = async (username: string, password: string): Promise<{ success: boolean; user?: User; token?: string; error?: string }> => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    const data = await response.json();

    if (response.ok && data.success) {
      return {
        success: true,
        user: data.user,
        token: data.token
      };
    } else {
      return {
        success: false,
        error: data.error || 'Login failed'
      };
    }
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: 'Network error occurred'
    };
  }
};

// Store user data in localStorage
export const storeUserData = (user: User, token: string): void => {
  try {
    localStorage.setItem('auth_token', token);
    localStorage.setItem('user_data', JSON.stringify(user));
    localStorage.setItem('session_created', new Date().toISOString());
  } catch (error) {
    console.error('Error storing user data:', error);
    throw new Error('Failed to store session data');
  }
};