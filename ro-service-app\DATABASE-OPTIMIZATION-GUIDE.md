# Database & Performance Optimization Guide

## Overview

This guide covers the comprehensive database optimization implementation for the RO Service Manager application, including indexing strategies, query optimization, performance monitoring, and best practices.

## 🚀 Quick Start

### Initialize Database with Optimal Settings
```bash
# Install dependencies
npm install

# Initialize database with optimal indexes
npm run db:init

# Check database statistics
npm run db:stats

# Seed with sample data
npm run db:seed
```

## 📊 Database Architecture

### Collections & Indexes

#### **Clients Collection**
- **Primary Index**: `{ nextServiceDate: 1, serviceType: 1 }` - Dashboard queries
- **Search Index**: `{ name: "text", location: "text", phone: "text" }` - Full-text search
- **Unique Index**: `{ phone: 1 }` - Prevent duplicate phone numbers
- **Service Type Index**: `{ serviceType: 1, nextServiceDate: 1 }` - Filtering
- **Date Range Index**: `{ nextServiceDate: 1, updatedAt: -1 }` - Overdue services
- **Scheduled Index**: `{ scheduledDate: 1 }` (sparse) - Scheduled services only
- **Recurring Index**: `{ serviceType: 1, serviceInterval: 1 }` - Recurring services

#### **Services Collection**
- **Primary Index**: `{ clientId: 1, serviceDate: -1 }` - Client service history
- **Status Index**: `{ status: 1, serviceDate: -1 }` - Status-based queries
- **Date Index**: `{ serviceDate: 1, status: 1 }` - Date range queries
- **Completed Index**: `{ status: 1, completedAt: -1 }` - Completed services only
- **Analytics Index**: `{ serviceDate: 1, clientId: 1, status: 1 }` - Dashboard analytics
- **TTL Index**: `{ createdAt: 1 }` - Auto-cleanup cancelled services (1 year)

#### **Users Collection**
- **Unique Username**: `{ username: 1 }` - Primary login field
- **Unique Email**: `{ email: 1 }` (sparse) - Optional email field
- **Active Users**: `{ isActive: 1, role: 1 }` - User management
- **Login Tracking**: `{ lastLogin: -1 }` (sparse) - Login analytics
- **Role Management**: `{ role: 1, createdAt: -1 }` - Admin queries

## ⚡ Performance Optimizations

### Connection Pool Settings
```javascript
{
  maxPoolSize: 10,              // Max 10 concurrent connections
  serverSelectionTimeoutMS: 5000, // 5s server selection timeout
  socketTimeoutMS: 45000,       // 45s socket timeout
  heartbeatFrequencyMS: 10000,  // 10s heartbeat
  retryWrites: true,            // Retry failed writes
  retryReads: true,             // Retry failed reads
  compressors: ['zlib'],        // Enable compression
  readPreference: 'primary',    // Read from primary
  writeConcern: {
    w: 'majority',              // Wait for majority acknowledgment
    j: true,                    // Wait for journal
    wtimeout: 5000              // 5s write timeout
  }
}
```

### Query Optimizations

#### **Dashboard Queries**
- **Field Selection**: Only select needed fields with `.select()`
- **Lean Queries**: Use `.lean()` for read-only operations (30-50% faster)
- **Service Type Filtering**: Exclude `serviceType: 'none'` clients
- **Pagination**: Implement `skip()` and `limit()` for large datasets

#### **Search Functionality**
- **Text Search**: Use `$text` search for queries ≥3 characters
- **Regex Fallback**: Use regex for shorter search terms
- **Index Hints**: MongoDB automatically uses optimal indexes

#### **API Optimizations**
- **Parallel Queries**: Use `Promise.all()` for independent operations
- **Population Limits**: Only populate required fields
- **Caching**: Implement query result caching for static data

## 🔍 Performance Monitoring

### Query Monitoring
```typescript
import { enableQueryMonitoring, getQueryStats } from '@/lib/query-monitor';

// Enable monitoring with 50ms slow query threshold
enableQueryMonitoring({ slowQueryThreshold: 50 });

// Get performance statistics
const stats = getQueryStats();
```

### Database Statistics API
```bash
# Get comprehensive database statistics
GET /api/admin/database-stats

# Trigger index optimization
POST /api/admin/database-stats
```

### Health Check Endpoint
```bash
# Enhanced health check with performance metrics
GET /api/health
```

## 📈 Performance Metrics

### Key Performance Indicators
- **Query Execution Time**: Target <100ms for 95% of queries
- **Index Usage Rate**: Target >90% index usage
- **Connection Pool**: Monitor active connections
- **Memory Usage**: Track heap usage and growth
- **Slow Query Count**: Monitor queries >100ms

### Monitoring Commands
```bash
# Database statistics
npm run db:stats

# Query performance monitoring
curl http://localhost:3000/api/health

# Database optimization
npm run db:optimize
```

## 🛠️ Optimization Tools

### Database Optimizer
```typescript
import { dbOptimizer } from '@/lib/database-optimizer';

// Get optimization suggestions
const suggestions = await dbOptimizer.suggestIndexOptimizations();

// Enable query profiling
await dbOptimizer.enableProfiling(1);

// Get slow queries
const slowQueries = await dbOptimizer.getSlowQueries(10);
```

### Query Monitor
```typescript
import { queryMonitor } from '@/lib/query-monitor';

// Get recent slow queries
const slowQueries = queryMonitor.getSlowQueries(20);

// Get performance summary
const summary = queryMonitor.getPerformanceSummary();
```

## 🎯 Best Practices

### Index Design
1. **Compound Indexes**: Design for query patterns, not individual fields
2. **Index Order**: Most selective fields first
3. **Sparse Indexes**: Use for optional fields
4. **TTL Indexes**: Auto-cleanup old data
5. **Partial Indexes**: Filter conditions for specific use cases

### Query Optimization
1. **Field Selection**: Always use `.select()` to limit returned fields
2. **Lean Queries**: Use `.lean()` for read-only operations
3. **Pagination**: Implement proper pagination with `skip()` and `limit()`
4. **Aggregation**: Use aggregation pipeline for complex queries
5. **Batch Operations**: Use `bulkWrite()` for multiple operations

### Connection Management
1. **Connection Pooling**: Configure appropriate pool size
2. **Connection Reuse**: Reuse connections across requests
3. **Graceful Shutdown**: Properly close connections on app termination
4. **Error Handling**: Implement robust error handling and retries

## 🚨 Troubleshooting

### Common Issues

#### Slow Queries
```bash
# Check slow queries
db.system.profile.find().sort({ts: -1}).limit(5)

# Explain query performance
db.clients.find({nextServiceDate: {$lt: new Date()}}).explain("executionStats")
```

#### Index Issues
```bash
# Check index usage
db.clients.aggregate([{$indexStats: {}}])

# Rebuild indexes
db.clients.reIndex()
```

#### Connection Problems
```bash
# Check connection status
db.adminCommand("connPoolStats")

# Monitor current operations
db.currentOp()
```

### Performance Tuning
1. **Monitor Slow Queries**: Set up alerts for queries >100ms
2. **Index Analysis**: Regular review of index usage statistics
3. **Connection Pool**: Adjust pool size based on load
4. **Memory Monitoring**: Track memory usage and optimize queries
5. **Regular Maintenance**: Schedule index rebuilds and statistics updates

## 📋 Maintenance Schedule

### Daily
- Monitor slow query logs
- Check connection pool metrics
- Review error logs

### Weekly
- Analyze query performance trends
- Review index usage statistics
- Check database growth metrics

### Monthly
- Full database statistics review
- Index optimization analysis
- Performance baseline updates
- Cleanup old profiling data

## 🔧 Configuration Files

### Environment Variables
```env
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/ro-service-manager
MONGODB_MAX_POOL_SIZE=10
MONGODB_SOCKET_TIMEOUT=45000

# Performance Monitoring
ENABLE_QUERY_MONITORING=true
SLOW_QUERY_THRESHOLD=100
```

### Next.js Configuration
```typescript
// next.config.ts
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['mongoose']
  }
};
```

This comprehensive optimization ensures your RO Service Manager application can handle production workloads efficiently with optimal database performance.
