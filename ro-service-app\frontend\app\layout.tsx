import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "../styles/globals.css";
import Navigation from "@/components/Navigation";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { AuthProvider } from "@/components/AuthContext";
import DailyNotification from "@/components/DailyNotification";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "RO Service Manager",
  description: "Comprehensive RO water service management application",
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased bg-slate-900 text-slate-100`}>
        <AuthProvider>
          <NotificationProvider>
            <div className="min-h-screen">
              <Navigation />
              <main className="pb-16 md:pb-0">
                {children}
              </main>
              <DailyNotification />
            </div>
          </NotificationProvider>
        </AuthProvider>
      </body>
    </html>
  );
}